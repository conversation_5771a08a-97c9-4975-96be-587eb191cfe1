"""
视频分析管道 - 完整的视频处理流水线
整合三层优化架构：预过滤层、聚合层、AI分析层
实现从视频帧到预警事件的完整处理流程
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import numpy as np

from .frame_buffer import FrameBufferManager, VideoFrame
from .pre_filter import PreFilterProcessor, FilterDecision, FilterDecisionType
from .frame_aggregator import FrameAggregator, FrameGroup, AggregationStrategy
from .ai_integration import AIIntegrationService, AnalysisRequest, AnalysisResponse
from .motion_detector import MotionDetectionManager, MotionResult
from ..config import ConfigurationManager
from ..alert import AlertService
from ...core.exceptions import VideoProcessingError

logger = logging.getLogger(__name__)


class PipelineStatus(Enum):
    """管道状态"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    PAUSING = "pausing"
    PAUSED = "paused"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class PipelineConfig:
    """管道配置"""
    # 处理设置
    batch_size: int = 10
    max_queue_size: int = 1000
    processing_interval: float = 0.1
    
    # 三层架构设置
    enable_pre_filter: bool = True
    enable_aggregation: bool = True
    enable_ai_analysis: bool = True
    
    # 性能设置
    max_workers: int = 4
    memory_limit_mb: int = 512
    
    # 监控设置
    stats_interval: int = 60
    health_check_interval: int = 30


@dataclass
class PipelineStatistics:
    """管道统计信息"""
    # 处理统计
    total_frames_processed: int = 0
    frames_filtered: int = 0
    frames_aggregated: int = 0
    frames_analyzed: int = 0
    alerts_generated: int = 0
    
    # 性能统计
    average_processing_time: float = 0.0
    peak_memory_usage: float = 0.0
    cpu_usage: float = 0.0
    
    # 吞吐量统计
    frames_per_second: float = 0.0
    alerts_per_minute: float = 0.0
    
    # 成本节省统计
    filter_efficiency: float = 0.0
    aggregation_ratio: float = 0.0
    cost_savings_percentage: float = 0.0
    
    # 时间统计
    uptime: timedelta = field(default_factory=timedelta)
    last_update: datetime = field(default_factory=datetime.utcnow)


class VideoPipeline:
    """视频分析管道"""
    
    def __init__(
        self,
        frame_buffer_manager: FrameBufferManager,
        config_manager: ConfigurationManager,
        alert_service: AlertService,
        pipeline_config: Optional[PipelineConfig] = None
    ):
        self.frame_buffer_manager = frame_buffer_manager
        self.config_manager = config_manager
        self.alert_service = alert_service
        self.config = pipeline_config or PipelineConfig()
        
        # 核心组件
        self.pre_filter: Optional[PreFilterProcessor] = None
        self.frame_aggregator: Optional[FrameAggregator] = None
        self.ai_integration: Optional[AIIntegrationService] = None
        self.motion_detector: Optional[MotionDetectionManager] = None
        
        # 管道状态
        self._status = PipelineStatus.STOPPED
        self._start_time: Optional[datetime] = None
        self._error_message: Optional[str] = None
        
        # 处理队列
        self._frame_queue: asyncio.Queue = asyncio.Queue(maxsize=self.config.max_queue_size)
        self._processing_tasks: List[asyncio.Task] = []
        
        # 统计信息
        self._stats = PipelineStatistics()
        self._processing_times: List[float] = []
        
        # 回调函数
        self._alert_callbacks: List[Callable[[Any], None]] = []
        self._status_callbacks: List[Callable[[PipelineStatus], None]] = []
        
        # 后台任务
        self._stats_task: Optional[asyncio.Task] = None
        self._health_check_task: Optional[asyncio.Task] = None
        
        logger.info("视频分析管道初始化完成")
    
    async def start(self):
        """启动管道"""
        if self._status != PipelineStatus.STOPPED:
            logger.warning(f"管道已在运行，当前状态: {self._status.value}")
            return
        
        try:
            await self._set_status(PipelineStatus.STARTING)
            self._start_time = datetime.utcnow()
            self._error_message = None
            
            # 初始化组件
            await self._initialize_components()
            
            # 启动处理任务
            await self._start_processing_tasks()
            
            # 启动后台任务
            await self._start_background_tasks()
            
            await self._set_status(PipelineStatus.RUNNING)
            logger.info("视频分析管道已启动")
            
        except Exception as e:
            self._error_message = str(e)
            await self._set_status(PipelineStatus.ERROR)
            logger.error(f"启动视频分析管道失败: {e}")
            raise VideoProcessingError(f"启动管道失败: {e}")
    
    async def stop(self):
        """停止管道"""
        if self._status == PipelineStatus.STOPPED:
            return
        
        try:
            await self._set_status(PipelineStatus.STOPPING)
            
            # 停止后台任务
            await self._stop_background_tasks()
            
            # 停止处理任务
            await self._stop_processing_tasks()
            
            # 清理组件
            await self._cleanup_components()
            
            # 清理队列
            while not self._frame_queue.empty():
                try:
                    self._frame_queue.get_nowait()
                    self._frame_queue.task_done()
                except asyncio.QueueEmpty:
                    break
            
            await self._set_status(PipelineStatus.STOPPED)
            logger.info("视频分析管道已停止")
            
        except Exception as e:
            self._error_message = str(e)
            await self._set_status(PipelineStatus.ERROR)
            logger.error(f"停止视频分析管道失败: {e}")
    
    async def pause(self):
        """暂停管道"""
        if self._status != PipelineStatus.RUNNING:
            logger.warning(f"管道未在运行，当前状态: {self._status.value}")
            return
        
        try:
            await self._set_status(PipelineStatus.PAUSING)
            
            # 暂停处理任务（不停止，只是暂停处理）
            for task in self._processing_tasks:
                if not task.done():
                    task.cancel()
            
            await self._set_status(PipelineStatus.PAUSED)
            logger.info("视频分析管道已暂停")
            
        except Exception as e:
            self._error_message = str(e)
            await self._set_status(PipelineStatus.ERROR)
            logger.error(f"暂停视频分析管道失败: {e}")
    
    async def resume(self):
        """恢复管道"""
        if self._status != PipelineStatus.PAUSED:
            logger.warning(f"管道未暂停，当前状态: {self._status.value}")
            return
        
        try:
            # 重新启动处理任务
            await self._start_processing_tasks()
            
            await self._set_status(PipelineStatus.RUNNING)
            logger.info("视频分析管道已恢复")
            
        except Exception as e:
            self._error_message = str(e)
            await self._set_status(PipelineStatus.ERROR)
            logger.error(f"恢复视频分析管道失败: {e}")
    
    async def process_frame(self, frame: VideoFrame) -> bool:
        """处理单个帧"""
        try:
            if self._status != PipelineStatus.RUNNING:
                logger.debug(f"管道未运行，跳过帧 {frame.frame_id}")
                return False
            
            # 添加到处理队列
            try:
                await asyncio.wait_for(
                    self._frame_queue.put(frame),
                    timeout=1.0  # 1秒超时
                )
                return True
            except asyncio.TimeoutError:
                logger.warning(f"帧队列已满，丢弃帧 {frame.frame_id}")
                return False
            
        except Exception as e:
            logger.error(f"处理帧 {frame.frame_id} 失败: {e}")
            return False
    
    def add_alert_callback(self, callback: Callable[[Any], None]):
        """添加预警回调函数"""
        self._alert_callbacks.append(callback)
        logger.debug("已添加预警回调函数")
    
    def remove_alert_callback(self, callback: Callable[[Any], None]):
        """移除预警回调函数"""
        try:
            self._alert_callbacks.remove(callback)
            logger.debug("已移除预警回调函数")
        except ValueError:
            pass
    
    def add_status_callback(self, callback: Callable[[PipelineStatus], None]):
        """添加状态变更回调函数"""
        self._status_callbacks.append(callback)
        logger.debug("已添加状态变更回调函数")
    
    def remove_status_callback(self, callback: Callable[[PipelineStatus], None]):
        """移除状态变更回调函数"""
        try:
            self._status_callbacks.remove(callback)
            logger.debug("已移除状态变更回调函数")
        except ValueError:
            pass
    
    async def _initialize_components(self):
        """初始化组件"""
        try:
            # 初始化运动检测管理器
            self.motion_detector = MotionDetectionManager()
            # 运动检测管理器不需要启动，它是一个管理器类
            
            # 初始化预过滤处理器
            if self.config.enable_pre_filter:
                from .pre_filter import PreFilterProcessor, PreFilterConfig
                self.pre_filter = PreFilterProcessor(PreFilterConfig())
                # 预过滤处理器不需要启动，初始化即可使用
            
            # 初始化帧聚合器
            if self.config.enable_aggregation:
                from .frame_aggregator import FrameAggregator, AggregationStrategy
                self.frame_aggregator = FrameAggregator(
                    aggregation_strategy=AggregationStrategy.HYBRID,
                    max_group_size=self.config.batch_size
                )
                # 帧聚合器不需要启动，初始化即可使用
            
            # 初始化AI集成服务
            if self.config.enable_ai_analysis:
                from .ai_integration import AIIntegrationService
                from ..ai import AIAnalyzer
                from ..storage.file_storage_service import FileStorageService

                # 创建必要的服务实例
                ai_analyzer = AIAnalyzer()
                storage_service = FileStorageService()

                self.ai_integration = AIIntegrationService(
                    ai_analyzer=ai_analyzer,
                    alert_service=self.alert_service,
                    storage_service=storage_service,
                    max_concurrent_analyses=self.config.max_workers
                )
                await self.ai_integration.start()
            
            logger.info("管道组件初始化完成")
            
        except Exception as e:
            logger.error(f"初始化管道组件失败: {e}")
            raise
    
    async def _cleanup_components(self):
        """清理组件"""
        try:
            components = [
                self.ai_integration,
                self.frame_aggregator,
                self.pre_filter,
                self.motion_detector
            ]
            
            for component in components:
                if component:
                    try:
                        # 只对有stop方法的组件调用停止
                        if hasattr(component, 'stop') and callable(getattr(component, 'stop')):
                            await component.stop()
                        else:
                            logger.debug(f"组件 {type(component).__name__} 不需要停止操作")
                    except Exception as e:
                        logger.error(f"停止组件失败: {e}")
            
            # 清空组件引用
            self.ai_integration = None
            self.frame_aggregator = None
            self.pre_filter = None
            self.motion_detector = None
            
            logger.info("管道组件清理完成")
            
        except Exception as e:
            logger.error(f"清理管道组件失败: {e}")
    
    async def _start_processing_tasks(self):
        """启动处理任务"""
        try:
            # 启动多个处理工作器
            self._processing_tasks = []
            for i in range(self.config.max_workers):
                task = asyncio.create_task(self._processing_worker(f"worker-{i}"))
                self._processing_tasks.append(task)
            
            logger.info(f"已启动 {len(self._processing_tasks)} 个处理工作器")
            
        except Exception as e:
            logger.error(f"启动处理任务失败: {e}")
            raise
    
    async def _stop_processing_tasks(self):
        """停止处理任务"""
        try:
            # 取消所有处理任务
            for task in self._processing_tasks:
                if not task.done():
                    task.cancel()
            
            # 等待任务完成
            if self._processing_tasks:
                await asyncio.gather(*self._processing_tasks, return_exceptions=True)
            
            self._processing_tasks.clear()
            logger.info("处理任务已停止")
            
        except Exception as e:
            logger.error(f"停止处理任务失败: {e}")
    
    async def _start_background_tasks(self):
        """启动后台任务"""
        try:
            # 启动统计任务
            self._stats_task = asyncio.create_task(self._stats_loop())
            
            # 启动健康检查任务
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            
            logger.info("后台任务已启动")
            
        except Exception as e:
            logger.error(f"启动后台任务失败: {e}")
            raise
    
    async def _stop_background_tasks(self):
        """停止后台任务"""
        try:
            # 停止后台任务
            for task in [self._stats_task, self._health_check_task]:
                if task and not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            
            self._stats_task = None
            self._health_check_task = None
            
            logger.info("后台任务已停止")
            
        except Exception as e:
            logger.error(f"停止后台任务失败: {e}")
    
    async def _processing_worker(self, worker_name: str):
        """处理工作器"""
        logger.info(f"处理工作器 {worker_name} 已启动")
        
        try:
            while self._status == PipelineStatus.RUNNING:
                try:
                    # 获取帧
                    frame = await asyncio.wait_for(
                        self._frame_queue.get(),
                        timeout=self.config.processing_interval
                    )
                    
                    # 处理帧
                    start_time = asyncio.get_event_loop().time()
                    await self._process_single_frame(frame)
                    processing_time = asyncio.get_event_loop().time() - start_time
                    
                    # 记录处理时间
                    self._processing_times.append(processing_time)
                    if len(self._processing_times) > 1000:
                        self._processing_times.pop(0)
                    
                    # 更新统计
                    self._stats.total_frames_processed += 1
                    
                    # 标记任务完成
                    self._frame_queue.task_done()
                    
                except asyncio.TimeoutError:
                    # 超时是正常的，继续循环
                    continue
                except Exception as e:
                    logger.error(f"工作器 {worker_name} 处理帧失败: {e}")
                    await asyncio.sleep(1)  # 错误后短暂延迟
        
        except asyncio.CancelledError:
            logger.info(f"处理工作器 {worker_name} 被取消")
        except Exception as e:
            logger.error(f"处理工作器 {worker_name} 错误: {e}")
        finally:
            logger.info(f"处理工作器 {worker_name} 已退出")
    
    async def _process_single_frame(self, frame: VideoFrame):
        """处理单个帧的完整流程"""
        try:
            # 第一层：预过滤
            if self.pre_filter and self.config.enable_pre_filter:
                decision = await self.pre_filter.should_process_frame(frame)
                
                if decision.decision_type == FilterDecisionType.SKIP:
                    self._stats.frames_filtered += 1
                    logger.debug(f"帧 {frame.frame_id} 被预过滤跳过: {decision.reason}")
                    return
                
                # 更新帧的优先级信息
                if decision.decision_type == FilterDecisionType.HIGH_PRIORITY:
                    frame.metadata["priority"] = "high"
                elif decision.decision_type == FilterDecisionType.BATCH_PROCESS:
                    frame.metadata["priority"] = "batch"
            
            # 第二层：帧聚合
            if self.frame_aggregator and self.config.enable_aggregation:
                aggregation_result = await self.frame_aggregator.add_frame(frame)
                
                if aggregation_result.should_process:
                    # 处理完成的帧组
                    for frame_group in aggregation_result.completed_groups:
                        await self._process_frame_group(frame_group)
                        self._stats.frames_aggregated += len(frame_group.frames)
                
                if not aggregation_result.should_process:
                    # 帧被聚合，暂不处理
                    return
            else:
                # 不使用聚合，直接处理单帧
                await self._process_frame_directly(frame)
        
        except Exception as e:
            logger.error(f"处理帧 {frame.frame_id} 失败: {e}")
    
    async def _process_frame_group(self, frame_group: FrameGroup):
        """处理帧组"""
        try:
            if not self.ai_integration or not self.config.enable_ai_analysis:
                return
            
            # 选择代表帧进行分析
            representative_frame = frame_group.representative_frame or frame_group.frames[0]
            
            # 创建分析请求
            analysis_request = AnalysisRequest(
                frames=[representative_frame],
                source_id=representative_frame.source_id,
                analysis_type="group_analysis",
                priority="normal" if representative_frame.metadata.get("priority") != "high" else "high",
                metadata={
                    "group_id": frame_group.group_id,
                    "group_size": len(frame_group.frames),
                    "time_span": (frame_group.end_time - frame_group.start_time).total_seconds(),
                    "aggregation_info": frame_group.aggregation_info
                }
            )
            
            # 提交分析
            await self.ai_integration.analyze_frames(analysis_request)
            self._stats.frames_analyzed += 1
            
        except Exception as e:
            logger.error(f"处理帧组 {frame_group.group_id} 失败: {e}")
    
    async def _process_frame_directly(self, frame: VideoFrame):
        """直接处理单帧"""
        try:
            if not self.ai_integration or not self.config.enable_ai_analysis:
                return
            
            # 创建分析请求
            analysis_request = AnalysisRequest(
                frames=[frame],
                source_id=frame.source_id,
                analysis_type="single_frame",
                priority=frame.metadata.get("priority", "normal"),
                metadata={
                    "frame_id": frame.frame_id,
                    "direct_processing": True
                }
            )
            
            # 提交分析
            await self.ai_integration.analyze_frames(analysis_request)
            self._stats.frames_analyzed += 1
            
        except Exception as e:
            logger.error(f"直接处理帧 {frame.frame_id} 失败: {e}")
    
    async def _stats_loop(self):
        """统计循环"""
        logger.info("统计任务已启动")
        
        while self._status in [PipelineStatus.RUNNING, PipelineStatus.PAUSED]:
            try:
                await asyncio.sleep(self.config.stats_interval)
                await self._update_statistics()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"统计循环错误: {e}")
                await asyncio.sleep(30)
    
    async def _health_check_loop(self):
        """健康检查循环"""
        logger.info("健康检查任务已启动")
        
        while self._status in [PipelineStatus.RUNNING, PipelineStatus.PAUSED]:
            try:
                await asyncio.sleep(self.config.health_check_interval)
                await self._health_check()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查循环错误: {e}")
                await asyncio.sleep(60)
    
    async def _update_statistics(self):
        """更新统计信息"""
        try:
            current_time = datetime.utcnow()
            
            # 计算平均处理时间
            if self._processing_times:
                self._stats.average_processing_time = sum(self._processing_times) / len(self._processing_times)
            
            # 计算运行时间
            if self._start_time:
                self._stats.uptime = current_time - self._start_time
            
            # 计算FPS
            if self._stats.uptime.total_seconds() > 0:
                self._stats.frames_per_second = self._stats.total_frames_processed / self._stats.uptime.total_seconds()
            
            # 计算预警频率
            if self._stats.uptime.total_seconds() > 0:
                self._stats.alerts_per_minute = (self._stats.alerts_generated * 60) / self._stats.uptime.total_seconds()
            
            # 计算过滤效率
            if self._stats.total_frames_processed > 0:
                self._stats.filter_efficiency = self._stats.frames_filtered / self._stats.total_frames_processed
            
            # 计算聚合比例
            if self._stats.frames_analyzed > 0:
                self._stats.aggregation_ratio = self._stats.frames_aggregated / self._stats.frames_analyzed
            
            # 计算成本节省
            if self._stats.total_frames_processed > 0:
                processed_frames = self._stats.total_frames_processed - self._stats.frames_filtered
                if processed_frames > 0:
                    self._stats.cost_savings_percentage = (self._stats.frames_filtered / self._stats.total_frames_processed) * 100
            
            self._stats.last_update = current_time
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    async def _health_check(self):
        """健康检查"""
        try:
            # 检查队列大小
            queue_size = self._frame_queue.qsize()
            if queue_size > self.config.max_queue_size * 0.8:
                logger.warning(f"帧队列接近满载: {queue_size}/{self.config.max_queue_size}")
            
            # 检查处理工作器状态
            active_workers = sum(1 for task in self._processing_tasks if not task.done())
            if active_workers < self.config.max_workers:
                logger.warning(f"部分处理工作器已停止: {active_workers}/{self.config.max_workers}")
            
            # 检查组件状态
            components = [
                ("motion_detector", self.motion_detector),
                ("pre_filter", self.pre_filter),
                ("frame_aggregator", self.frame_aggregator),
                ("ai_integration", self.ai_integration)
            ]
            
            for name, component in components:
                if component:
                    try:
                        health = await component.health_check()
                        if health.get("status") != "healthy":
                            logger.warning(f"组件 {name} 健康状态异常: {health}")
                    except Exception as e:
                        logger.error(f"检查组件 {name} 健康状态失败: {e}")
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
    
    async def _set_status(self, status: PipelineStatus):
        """设置管道状态"""
        if self._status != status:
            old_status = self._status
            self._status = status
            
            logger.info(f"管道状态变更: {old_status.value} -> {status.value}")
            
            # 通知状态变更回调
            for callback in self._status_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(status)
                    else:
                        callback(status)
                except Exception as e:
                    logger.error(f"状态变更回调失败: {e}")
    
    def get_status(self) -> PipelineStatus:
        """获取管道状态"""
        return self._status
    
    def get_statistics(self) -> PipelineStatistics:
        """获取统计信息"""
        return self._stats
    
    def get_config(self) -> PipelineConfig:
        """获取管道配置"""
        return self.config
    
    async def update_config(self, new_config: PipelineConfig):
        """更新管道配置"""
        try:
            old_config = self.config
            self.config = new_config
            
            # 如果管道正在运行，可能需要重启某些组件
            if self._status == PipelineStatus.RUNNING:
                logger.info("管道配置已更新，某些变更可能需要重启管道才能生效")
            
            logger.info("管道配置已更新")
            
        except Exception as e:
            logger.error(f"更新管道配置失败: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """获取管道健康状态"""
        try:
            health_status = {
                "status": "healthy",
                "pipeline_status": self._status.value,
                "uptime": self._stats.uptime.total_seconds(),
                "queue_size": self._frame_queue.qsize(),
                "active_workers": sum(1 for task in self._processing_tasks if not task.done()),
                "error_message": self._error_message,
                "statistics": {
                    "frames_processed": self._stats.total_frames_processed,
                    "frames_per_second": self._stats.frames_per_second,
                    "average_processing_time": self._stats.average_processing_time,
                    "filter_efficiency": self._stats.filter_efficiency,
                    "cost_savings": self._stats.cost_savings_percentage
                },
                "issues": []
            }
            
            # 检查各种健康指标
            if self._status == PipelineStatus.ERROR:
                health_status["status"] = "error"
                health_status["issues"].append(f"管道错误: {self._error_message}")
            
            if self._frame_queue.qsize() > self.config.max_queue_size * 0.9:
                health_status["issues"].append("帧队列接近满载")
            
            active_workers = sum(1 for task in self._processing_tasks if not task.done())
            if active_workers < self.config.max_workers * 0.5:
                health_status["issues"].append("处理工作器数量不足")
            
            if self._stats.frames_per_second < 1.0 and self._stats.total_frames_processed > 100:
                health_status["issues"].append("处理帧率过低")
            
            if health_status["issues"]:
                health_status["status"] = "warning" if len(health_status["issues"]) < 3 else "unhealthy"
            
            return health_status
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "pipeline_status": self._status.value
            } 