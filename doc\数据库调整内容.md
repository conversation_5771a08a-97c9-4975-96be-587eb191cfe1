
📊 数据库实体调整分析
🔍 需要调整的实体对比
1. Store (门店) → v_stores
现有模型字段:

store_name, store_code, address, contact_info, description
数据库设计字段:

name, code, type, district, address, phone, business_hours, manager_name, manager_phone, status, camera_count, online_camera_count, coordinates, metadata
需要调整:

✅ store_name → name
✅ store_code → code
➕ 新增: type, district, phone, business_hours, manager_name, manager_phone, status, camera_count, online_camera_count, coordinates, metadata
➖ 移除: contact_info, description
2. Camera (摄像头) → v_cameras
现有模型字段:

name, code, store_id, rtsp_url, username, password, location, ip_address, port, resolution, fps, is_active, is_online, description
数据库设计字段:

store_id, name, code, location, specific_location, ip_address, mac_address, rtsp_url, rtsp_backup_url, brand, model, resolution, fps, analysis_enabled, analysis_fps, analysis_scenarios, status, last_heartbeat_at, connection_info, position_x, position_y, view_angle, metadata
需要调整:

✅ 保持: name, code, store_id, rtsp_url, location, ip_address, resolution, fps
🔄 is_active + is_online → status (枚举值)
➕ 新增: specific_location, mac_address, rtsp_backup_url, brand, model, analysis_enabled, analysis_fps, analysis_scenarios, last_heartbeat_at, connection_info, position_x, position_y, view_angle, metadata
➖ 移除: username, password, port, description
3. AnomalyBehavior (异常行为) → v_anomaly_behaviors
现有模型字段:

behavior_name, behavior_code, description, ai_keywords, severity_level, default_confidence_threshold, is_active, detection_config
数据库设计字段:

name, code, category, description, ai_keywords, default_severity, default_confidence_threshold, is_enabled, sort_order, metadata
需要调整:

✅ behavior_name → name
✅ behavior_code → code
✅ severity_level → default_severity
✅ is_active → is_enabled
➕ 新增: category, sort_order, metadata
➖ 移除: detection_config
4. AnalysisRule (分析规则) → v_analysis_rules
现有模型字段:

store_id, camera_id, rule_name, rule_code, description, is_active, confidence_threshold, continuous_frames, time_window, cooldown_period, trigger_config, start_time, end_time, effective_days
数据库设计字段:

name, store_id, camera_id, rule_type, trigger_conditions, confidence_threshold, consecutive_frames, time_window_seconds, cooldown_seconds, severity_override, apply_scope, is_enabled, priority, metadata
需要调整:

✅ rule_name → name
✅ is_active → is_enabled
✅ continuous_frames → consecutive_frames
✅ time_window → time_window_seconds
✅ cooldown_period → cooldown_seconds
✅ trigger_config → trigger_conditions
➕ 新增: rule_type, severity_override, apply_scope, priority, metadata
➖ 移除: rule_code, description, start_time, end_time, effective_days
5. RuleBehaviorMapping (规则行为映射) → v_rule_behavior_mappings
现有模型字段:

analysis_rule_id, anomaly_behavior_id, is_active, custom_confidence_threshold, custom_continuous_frames, custom_time_window, custom_cooldown_period, custom_config
数据库设计字段:

rule_id, behavior_id, is_enabled, confidence_threshold_override, severity_override, weight, custom_params
需要调整:

✅ analysis_rule_id → rule_id
✅ anomaly_behavior_id → behavior_id
✅ is_active → is_enabled
✅ custom_confidence_threshold → confidence_threshold_override
✅ custom_config → custom_params
➕ 新增: severity_override, weight
➖ 移除: custom_continuous_frames, custom_time_window, custom_cooldown_period
6. PromptTemplate (提示词模板) → v_prompt_templates
现有模型字段:

template_name, template_code, template_type, description, is_active, anomaly_behavior_id, prompt_content, variables, priority, usage_count
数据库设计字段:

name, code, category, behavior_id, scenario, template_content, variables, model_type, model_version, temperature, max_tokens, system_prompt, is_enabled, is_default, version, performance_score, usage_count, success_rate, avg_response_time_ms
需要调整:

✅ template_name → name
✅ template_code → code
✅ template_type → category
✅ anomaly_behavior_id → behavior_id
✅ prompt_content → template_content
✅ is_active → is_enabled
➕ 新增: scenario, model_type, model_version, temperature, max_tokens, system_prompt, is_default, version, performance_score, success_rate, avg_response_time_ms
➖ 移除: description, priority
7. AlertEvent (预警事件) → v_alerts
现有模型字段:

store_id, camera_id, anomaly_behavior_id, analysis_rule_id, alert_title, alert_description, alert_level, alert_time, ai_analysis_result, confidence_score, detected_objects, status, processed_at, processed_by, process_notes, notification_sent, notification_channels, notification_time
数据库设计字段:

uuid, store_id, camera_id, behavior_id, rule_id, title, description, severity, confidence_score, status, trigger_time, first_detected_at, last_detected_at, detection_count, ai_analysis_result, trigger_frames_info, location_info, assigned_to, processed_by, processed_at, resolution_notes, evidence_count, notification_sent, is_important, tags, metadata
需要调整:

✅ anomaly_behavior_id → behavior_id
✅ analysis_rule_id → rule_id
✅ alert_title → title
✅ alert_description → description
✅ alert_level → severity
✅ alert_time → trigger_time
✅ process_notes → resolution_notes
➕ 新增: uuid, first_detected_at, last_detected_at, detection_count, trigger_frames_info, location_info, assigned_to, evidence_count, is_important, tags, metadata
➖ 移除: detected_objects, notification_channels, notification_time
8. EvidenceFile (证据文件) → v_evidence_files
现有模型字段:

alert_event_id, camera_id, file_name, file_path, file_url, file_type, file_size, mime_type, duration, width, height, fps, storage_type, bucket_name, object_key, is_processed, is_deleted, delete_reason, captured_at, uploaded_at
数据库设计字段:

alert_id, file_storage_id, evidence_type, capture_time, frame_index, detection_confidence, is_key_evidence, evidence_category, analysis_result, legal_status, chain_of_custody, retention_reason
需要调整:

✅ alert_event_id → alert_id
✅ captured_at → capture_time
➕ 新增: file_storage_id, evidence_type, frame_index, detection_confidence, is_key_evidence, evidence_category, analysis_result, legal_status, chain_of_custody, retention_reason
➖ 移除: camera_id, file_name, file_path, file_url, file_type, file_size, mime_type, duration, width, height, fps, storage_type, bucket_name, object_key, is_processed, is_deleted, delete_reason, uploaded_at
📋 总结
需要调整的实体共 8个，主要变化包括：

字段重命名: 统一命名规范
新增字段: 增加更多业务字段和元数据支持
移除字段: 简化或重构部分字段
类型调整: 部分字段类型和约束的调整
关系调整: 外键关系的调整
这些调整主要是为了：

统一数据库命名规范
增强业务功能支持
提高数据完整性
支持多租户架构
增加审计和元数据功能