#!/usr/bin/env python3
"""
数据库配置调试工具
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def main():
    print("🔍 数据库配置调试工具")
    print("=" * 50)
    
    print("=== 环境变量 ===")
    db_vars = ['DB_HOST', 'DB_PORT', 'DB_USER', 'DB_PASSWORD', 'DB_NAME']
    for var in db_vars:
        value = os.getenv(var, 'NOT_SET')
        print(f"   {var}: '{value}'")
    
    print("\n=== 服务器环境变量 ===")
    server_vars = ['HOST', 'PORT']
    for var in server_vars:
        value = os.getenv(var, 'NOT_SET')
        print(f"   {var}: '{value}'")
    
    try:
        from app.core.config import settings
        
        print("\n=== 数据库配置 ===")
        print(f"   host: {settings.database.host}")
        print(f"   port: {settings.database.port}")
        print(f"   username: {settings.database.username}")
        print(f"   password: {'*' * len(settings.database.password) if settings.database.password else 'EMPTY'}")
        print(f"   database: {settings.database.database}")
        print(f"   url: {settings.database.url}")
        
        print("\n=== 服务器配置 ===")
        print(f"   host: {settings.server.host}")
        print(f"   port: {settings.server.port}")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
