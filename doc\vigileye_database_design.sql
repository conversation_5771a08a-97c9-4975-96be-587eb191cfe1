/*
 Navicat Premium Dump SQL

 Source Server         : mysql_localhost
 Source Server Type    : MySQL
 Source Server Version : 80019 (8.0.19)
 Source Host           : localhost:3306
 Source Schema         : vigileye

 Target Server Type    : MySQL
 Target Server Version : 80019 (8.0.19)
 File Encoding         : 65001

 Date: 21/07/2025 18:21:03
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for v_alerts
-- ----------------------------
DROP TABLE IF EXISTS `v_alerts`;
CREATE TABLE `v_alerts`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '预警ID',
  `uuid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '预警唯一标识',
  `store_id` bigint UNSIGNED NOT NULL COMMENT '门店ID',
  `camera_id` bigint UNSIGNED NOT NULL COMMENT '摄像头ID',
  `behavior_id` bigint UNSIGNED NOT NULL COMMENT '异常行为ID',
  `rule_id` bigint UNSIGNED NOT NULL COMMENT '触发规则ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '预警标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '预警描述',
  `severity` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '严重等级',
  `confidence_score` decimal(5, 4) NOT NULL COMMENT '置信度分数',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '处理状态',
  `trigger_time` timestamp NOT NULL COMMENT '触发时间',
  `first_detected_at` timestamp NOT NULL COMMENT '首次检测时间',
  `last_detected_at` timestamp NOT NULL COMMENT '最后检测时间',
  `detection_count` int UNSIGNED NOT NULL DEFAULT 1 COMMENT '检测次数',
  `ai_analysis_result` json NULL COMMENT 'AI分析原始结果',
  `trigger_frames_info` json NULL COMMENT '触发帧信息',
  `location_info` json NULL COMMENT '位置信息',
  `assigned_to` bigint UNSIGNED NULL DEFAULT NULL COMMENT '分配处理人',
  `processed_by` bigint UNSIGNED NULL DEFAULT NULL COMMENT '实际处理人',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `resolution_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '处理备注',
  `evidence_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '证据文件数量',
  `notification_sent` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已发送通知',
  `is_important` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否重要事件',
  `tags` json NULL COMMENT '事件标签',
  `metadata` json NULL COMMENT '扩展元数据',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10019 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '预警事件表' ROW_FORMAT = DYNAMIC;
-- ----------------------------
-- Table structure for v_analysis_rules
-- ----------------------------
DROP TABLE IF EXISTS `v_analysis_rules`;
CREATE TABLE `v_analysis_rules`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则名称',
  `store_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '适用门店ID（NULL表示全局）',
  `camera_id` bigint UNSIGNED NOT NULL COMMENT '摄像头ID',
  `rule_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'simple' COMMENT '规则类型',
  `trigger_conditions` json NOT NULL COMMENT '触发条件配置',
  `confidence_threshold` decimal(3, 2) NOT NULL DEFAULT 0.70 COMMENT '置信度阈值',
  `consecutive_frames` int UNSIGNED NOT NULL DEFAULT 3 COMMENT '连续帧数要求',
  `time_window_seconds` int UNSIGNED NULL DEFAULT 30 COMMENT '时间窗口（秒）',
  `cooldown_seconds` int UNSIGNED NULL DEFAULT 60 COMMENT '冷却时间（秒）',
  `severity_override` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '严重等级覆盖',
  `apply_scope` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'all_cameras' COMMENT '应用范围',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `priority` int UNSIGNED NOT NULL DEFAULT 100 COMMENT '规则优先级',
  `metadata` json NULL COMMENT '扩展配置',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI分析规则表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for v_anomaly_behaviors
-- ----------------------------
DROP TABLE IF EXISTS `v_anomaly_behaviors`;
CREATE TABLE `v_anomaly_behaviors`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '行为ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '行为名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '行为代码',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '行为分类',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '行为描述',
  `ai_keywords` json NOT NULL COMMENT 'AI识别关键词',
  `default_severity` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'medium' COMMENT '默认严重等级',
  `default_confidence_threshold` decimal(3, 2) NOT NULL DEFAULT 0.70 COMMENT '默认置信度阈值',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序权重',
  `metadata` json NULL COMMENT '扩展元数据',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '异常行为定义表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for v_api_keys
-- ----------------------------
DROP TABLE IF EXISTS `v_api_keys`;
CREATE TABLE `v_api_keys`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '密钥ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密钥名称',
  `service_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '服务类型',
  `provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '服务提供商',
  `key_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '密钥标识',
  `encrypted_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '加密后的密钥',
  `encryption_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'AES-256' COMMENT '加密方法',
  `config` json NULL COMMENT '额外配置信息',
  `quota_limit` bigint UNSIGNED NULL DEFAULT NULL COMMENT '配额限制',
  `quota_used` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '已使用配额',
  `quota_reset_date` date NULL DEFAULT NULL COMMENT '配额重置日期',
  `rate_limit_per_minute` int UNSIGNED NULL DEFAULT NULL COMMENT '每分钟限制',
  `rate_limit_per_hour` int UNSIGNED NULL DEFAULT NULL COMMENT '每小时限制',
  `rate_limit_per_day` int UNSIGNED NULL DEFAULT NULL COMMENT '每天限制',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'API密钥管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for v_cameras
-- ----------------------------
DROP TABLE IF EXISTS `v_cameras`;
CREATE TABLE `v_cameras`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '摄像头ID',
  `store_id` bigint UNSIGNED NOT NULL COMMENT '所属门店ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '摄像头名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备编号',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '安装位置描述',
  `specific_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '具体位置',
  `ip_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `mac_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'MAC地址',
  `rtsp_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'RTSP流地址',
  `rtsp_backup_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备用RTSP流地址',
  `brand` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备品牌',
  `model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备型号',
  `resolution` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分辨率',
  `fps` int UNSIGNED NULL DEFAULT 25 COMMENT '帧率',
  `analysis_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用AI分析',
  `analysis_fps` decimal(3, 1) NULL DEFAULT 1.0 COMMENT 'AI分析帧率',
  `analysis_scenarios` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '适用分析场景',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'offline' COMMENT '设备状态',
  `last_heartbeat_at` timestamp NULL DEFAULT NULL COMMENT '最后心跳时间',
  `connection_info` json NULL COMMENT '连接信息',
  `position_x` decimal(10, 6) NULL DEFAULT NULL COMMENT 'X坐标（相对位置）',
  `position_y` decimal(10, 6) NULL DEFAULT NULL COMMENT 'Y坐标（相对位置）',
  `view_angle` int NULL DEFAULT NULL COMMENT '视角角度',
  `metadata` json NULL COMMENT '扩展元数据',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code` ASC) USING BTREE,
  INDEX `idx_store_id`(`store_id` ASC) USING BTREE,
  INDEX `idx_name`(`name` ASC) USING BTREE,
  INDEX `idx_location`(`location` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_analysis_enabled`(`analysis_enabled` ASC) USING BTREE,
  INDEX `idx_last_heartbeat`(`last_heartbeat_at` ASC) USING BTREE,
  CONSTRAINT `fk_v_cameras_store` FOREIGN KEY (`store_id`) REFERENCES `v_stores` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '摄像头设备表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for v_evidence_files
-- ----------------------------
DROP TABLE IF EXISTS `v_evidence_files`;
CREATE TABLE `v_evidence_files`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '证据ID',
  `alert_id` bigint UNSIGNED NOT NULL COMMENT '关联预警ID',
  `file_storage_id` bigint UNSIGNED NOT NULL COMMENT '关联文件存储ID',
  `evidence_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'auto_capture' COMMENT '证据类型',
  `capture_time` timestamp NOT NULL COMMENT '捕获时间',
  `frame_index` int UNSIGNED NULL DEFAULT NULL COMMENT '帧索引（图片）',
  `detection_confidence` decimal(5, 4) NULL DEFAULT NULL COMMENT '检测置信度',
  `is_key_evidence` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关键证据',
  `evidence_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'during_event' COMMENT '证据时间分类',
  `analysis_result` json NULL COMMENT '分析结果',
  `legal_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '法律状态',
  `chain_of_custody` json NULL COMMENT '监管链记录',
  `retention_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '保留原因',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '证据文件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for v_file_storage
-- ----------------------------
DROP TABLE IF EXISTS `v_file_storage`;
CREATE TABLE `v_file_storage`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `uuid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件唯一标识',
  `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `file_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件类型',
  `mime_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'MIME类型',
  `file_size` bigint UNSIGNED NOT NULL COMMENT '文件大小（字节）',
  `file_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件哈希值',
  `checksum` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '校验和',
  `storage_provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'minio' COMMENT '存储提供商',
  `storage_bucket` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储桶',
  `storage_key` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储键',
  `storage_region` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '存储区域',
  `access_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '访问URL',
  `thumbnail_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '缩略图路径',
  `preview_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '预览文件路径',
  `metadata` json NULL COMMENT '文件元数据',
  `exif_data` json NULL COMMENT 'EXIF数据（图片）',
  `duration_seconds` decimal(8, 3) NULL DEFAULT NULL COMMENT '时长（视频/音频）',
  `resolution` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分辨率',
  `bitrate` int UNSIGNED NULL DEFAULT NULL COMMENT '比特率',
  `encoding` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '编码格式',
  `is_encrypted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否加密',
  `encryption_key_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '加密密钥ID',
  `compression_ratio` decimal(5, 4) NULL DEFAULT NULL COMMENT '压缩比',
  `upload_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '上传来源',
  `upload_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '上传IP',
  `uploaded_by` bigint UNSIGNED NOT NULL COMMENT '上传人ID',
  `related_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联类型',
  `related_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联ID',
  `access_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'private' COMMENT '访问级别',
  `retention_policy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'standard' COMMENT '保留策略',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `last_accessed_at` timestamp NULL DEFAULT NULL COMMENT '最后访问时间',
  `access_count` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '访问次数',
  `download_count` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '下载次数',
  `is_virus_scanned` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已病毒扫描',
  `virus_scan_result` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '病毒扫描结果',
  `virus_scan_at` timestamp NULL DEFAULT NULL COMMENT '病毒扫描时间',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'uploading' COMMENT '文件状态',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '文件存储表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for v_prompt_templates
-- ----------------------------
DROP TABLE IF EXISTS `v_prompt_templates`;
CREATE TABLE `v_prompt_templates`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板代码',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板分类',
  `behavior_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '关联异常行为ID',
  `scenario` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '适用场景',
  `template_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板内容',
  `variables` json NULL COMMENT '模板变量定义',
  `model_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '适用AI模型类型',
  `model_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '适用模型版本',
  `temperature` decimal(3, 2) NULL DEFAULT 0.70 COMMENT '温度参数',
  `max_tokens` int UNSIGNED NULL DEFAULT 1000 COMMENT '最大令牌数',
  `system_prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '系统提示词',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否默认模板',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1.0' COMMENT '模板版本',
  `performance_score` decimal(5, 4) NULL DEFAULT NULL COMMENT '性能评分',
  `usage_count` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用次数',
  `success_rate` decimal(5, 4) NULL DEFAULT NULL COMMENT '成功率',
  `avg_response_time_ms` int UNSIGNED NULL DEFAULT NULL COMMENT '平均响应时间（毫秒）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '提示词模板管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for v_rule_behavior_mappings
-- ----------------------------
DROP TABLE IF EXISTS `v_rule_behavior_mappings`;
CREATE TABLE `v_rule_behavior_mappings`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `rule_id` bigint UNSIGNED NOT NULL COMMENT '规则ID',
  `behavior_id` bigint UNSIGNED NOT NULL COMMENT '异常行为ID',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用该行为检测',
  `confidence_threshold_override` decimal(3, 2) NULL DEFAULT NULL COMMENT '置信度阈值覆盖',
  `severity_override` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '严重等级覆盖',
  `weight` decimal(3, 2) NOT NULL DEFAULT 1.00 COMMENT '该行为在规则中的权重',
  `custom_params` json NULL COMMENT '该行为的自定义参数',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '规则异常行为关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for v_stores
-- ----------------------------
DROP TABLE IF EXISTS `v_stores`;
CREATE TABLE `v_stores`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '门店ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '门店名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '门店编号',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'standard' COMMENT '门店类型',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所在区域',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `business_hours` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '营业时间',
  `manager_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '负责人姓名',
  `manager_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '负责人电话',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '门店状态',
  `camera_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '摄像头数量',
  `online_camera_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '在线摄像头数量',
  `coordinates` point NULL COMMENT '地理坐标',
  `metadata` json NULL COMMENT '扩展元数据',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code` ASC) USING BTREE,
  INDEX `idx_name`(`name` ASC) USING BTREE,
  INDEX `idx_district`(`district` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '门店表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for v_third_party_services
-- ----------------------------
DROP TABLE IF EXISTS `v_third_party_services`;
CREATE TABLE `v_third_party_services`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '服务ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '服务名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '服务代码',
  `service_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '服务类型',
  `provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '服务提供商',
  `endpoint_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '服务端点URL',
  `auth_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'api_key' COMMENT '认证类型',
  `auth_config` json NULL COMMENT '认证配置',
  `request_config` json NULL COMMENT '请求配置',
  `response_config` json NULL COMMENT '响应配置',
  `retry_config` json NULL COMMENT '重试配置',
  `timeout_seconds` int UNSIGNED NOT NULL DEFAULT 30 COMMENT '超时时间（秒）',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `health_check_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '健康检查URL',
  `health_check_interval_minutes` int UNSIGNED NULL DEFAULT 5 COMMENT '健康检查间隔（分钟）',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '服务状态',
  `last_health_check_at` timestamp NULL DEFAULT NULL COMMENT '最后健康检查时间',
  `last_error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '最后错误信息',
  `success_count` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '成功次数',
  `failure_count` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '失败次数',
  `avg_response_time_ms` int UNSIGNED NULL DEFAULT NULL COMMENT '平均响应时间（毫秒）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '第三方服务配置表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
