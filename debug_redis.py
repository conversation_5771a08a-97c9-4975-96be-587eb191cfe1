#!/usr/bin/env python3
"""
Redis配置调试脚本
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_redis_config():
    """测试Redis配置"""
    print("=== Redis配置测试 ===")
    
    try:
        from dotenv import load_dotenv
        
        # 加载环境变量
        load_dotenv('.env', encoding='utf-8')
        
        # 检查环境变量
        redis_host = os.getenv('REDIS_HOST')
        redis_port = os.getenv('REDIS_PORT')
        redis_password = os.getenv('REDIS_PASSWORD')
        redis_db = os.getenv('REDIS_DB')
        
        print(f"📋 环境变量:")
        print(f"   REDIS_HOST: {repr(redis_host)}")
        print(f"   REDIS_PORT: {repr(redis_port)}")
        print(f"   REDIS_PASSWORD: {repr(redis_password)}")
        print(f"   REDIS_DB: {repr(redis_db)}")
        
        # 测试配置类
        print(f"\n🔧 测试配置类...")
        from app.core.config import RedisSettings
        
        redis_settings = RedisSettings()
        print(f"   host: {redis_settings.host}")
        print(f"   port: {redis_settings.port}")
        print(f"   password: {repr(redis_settings.password)}")
        print(f"   database: {redis_settings.database}")
        print(f"   max_connections: {redis_settings.max_connections}")
        print(f"   timeout: {redis_settings.timeout}")
        print(f"   url: {redis_settings.url}")
        
        # 测试全局配置
        print(f"\n🌐 测试全局配置...")
        from app.core.config import settings
        
        print(f"   settings.redis.host: {settings.redis.host}")
        print(f"   settings.redis.port: {settings.redis.port}")
        print(f"   settings.redis.password: {repr(settings.redis.password)}")
        print(f"   settings.redis.database: {settings.redis.database}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_redis_connection():
    """测试Redis连接"""
    print("\n=== Redis连接测试 ===")
    
    try:
        import redis
        from app.core.config import settings
        
        print(f"🔗 尝试连接Redis...")
        print(f"   Host: {settings.redis.host}")
        print(f"   Port: {settings.redis.port}")
        print(f"   DB: {settings.redis.database}")
        
        # 创建Redis连接
        r = redis.Redis(
            host=settings.redis.host,
            port=settings.redis.port,
            password=settings.redis.password if settings.redis.password else None,
            db=settings.redis.database,
            decode_responses=True,
            socket_timeout=settings.redis.timeout,
            socket_connect_timeout=settings.redis.timeout
        )
        
        # 测试连接
        r.ping()
        print("✅ Redis连接成功")
        
        # 测试基本操作
        r.set("test_key", "test_value", ex=10)
        value = r.get("test_key")
        print(f"✅ Redis读写测试成功: {value}")
        
        # 清理测试数据
        r.delete("test_key")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False

def test_async_redis():
    """测试异步Redis连接"""
    print("\n=== 异步Redis连接测试 ===")
    
    try:
        import asyncio
        import redis.asyncio as redis
        from app.core.config import settings
        
        async def async_test():
            print(f"🔗 尝试异步连接Redis...")
            
            r = redis.Redis(
                host=settings.redis.host,
                port=settings.redis.port,
                password=settings.redis.password if settings.redis.password else None,
                db=settings.redis.database,
                decode_responses=True,
                socket_timeout=settings.redis.timeout,
                socket_connect_timeout=settings.redis.timeout
            )
            
            # 测试连接
            await r.ping()
            print("✅ 异步Redis连接成功")
            
            # 测试基本操作
            await r.set("async_test_key", "async_test_value", ex=10)
            value = await r.get("async_test_key")
            print(f"✅ 异步Redis读写测试成功: {value}")
            
            # 清理测试数据
            await r.delete("async_test_key")
            await r.close()
            
            return True
        
        return asyncio.run(async_test())
        
    except Exception as e:
        print(f"❌ 异步Redis连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 Redis配置和连接诊断工具")
    print("=" * 50)
    
    config_ok = test_redis_config()
    
    if config_ok:
        sync_ok = test_redis_connection()
        async_ok = test_async_redis()
        
        print("\n" + "=" * 50)
        print("🎯 诊断结果:")
        print(f"   配置加载: {'✅' if config_ok else '❌'}")
        print(f"   同步连接: {'✅' if sync_ok else '❌'}")
        print(f"   异步连接: {'✅' if async_ok else '❌'}")
        
        if config_ok and sync_ok and async_ok:
            print("🎉 Redis配置和连接都正常!")
        else:
            print("⚠️ 存在问题，请检查Redis服务是否运行")
    else:
        print("\n❌ 配置加载失败，请检查配置文件")

if __name__ == "__main__":
    main()
