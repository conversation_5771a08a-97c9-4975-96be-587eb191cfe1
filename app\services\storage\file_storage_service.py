"""
文件存储服务 - 基于MinIO的对象存储管理
"""
import asyncio
import logging
import hashlib
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, BinaryIO
from pathlib import Path
import mimetypes
import os

from minio import Minio
from minio.error import S3Error
import aiofiles

from app.core.config import settings
from app.core.exceptions import FileStorageException

logger = logging.getLogger(__name__)


class FileStorageService:
    """文件存储服务"""

    def __init__(self, lazy_init=True):
        self.minio_client = None
        self.bucket_name = settings.minio.bucket_name
        self._initialized = False

        if not lazy_init:
            self._initialize_client()

    def _ensure_initialized(self):
        """确保客户端已初始化"""
        if not self._initialized:
            self._initialize_client()
    
    def _initialize_client(self):
        """初始化MinIO客户端"""
        try:
            # 处理endpoint格式 - 移除协议前缀
            endpoint = settings.minio.endpoint
            if endpoint.startswith('http://'):
                endpoint = endpoint[7:]
            elif endpoint.startswith('https://'):
                endpoint = endpoint[8:]

            logger.info(f"🔗 初始化MinIO客户端...")
            logger.info(f"   Endpoint: {endpoint}")
            logger.info(f"   Secure: {settings.minio.secure}")
            logger.info(f"   Bucket: {settings.minio.bucket_name}")

            self.minio_client = Minio(
                endpoint,
                access_key=settings.minio.access_key,
                secret_key=settings.minio.secret_key,
                secure=settings.minio.secure
            )

            # 测试连接
            logger.info("🔍 测试MinIO连接...")
            buckets = self.minio_client.list_buckets()
            logger.info(f"✅ MinIO连接成功，发现 {len(buckets)} 个存储桶")

            # 确保存储桶存在
            self._ensure_bucket_exists()

            self._initialized = True
            logger.info("✅ MinIO客户端初始化成功")

        except Exception as e:
            logger.error(f"MinIO客户端初始化失败: {str(e)}")
            logger.error(f"   配置信息: endpoint={settings.minio.endpoint}, secure={settings.minio.secure}")
            logger.warning("⚠️ MinIO服务暂时不可用，文件存储功能将受限")
            # 不抛出异常，允许系统继续启动
            self._initialized = False
    
    def _ensure_bucket_exists(self):
        """确保存储桶存在"""
        try:
            if not self.minio_client.bucket_exists(self.bucket_name):
                self.minio_client.make_bucket(self.bucket_name, location=settings.minio.region)
                logger.info(f"创建存储桶: {self.bucket_name}")
            else:
                logger.debug(f"存储桶已存在: {self.bucket_name}")
                
        except S3Error as e:
            logger.error(f"存储桶操作失败: {str(e)}")
            raise FileStorageException(f"存储桶操作失败: {str(e)}")
    
    async def upload_file(
        self,
        file_path: str,
        content: BinaryIO,
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """上传文件"""
        try:
            self._ensure_initialized()
            if not self._initialized:
                raise FileStorageException("MinIO服务不可用")
            # 生成文件信息
            file_id = str(uuid.uuid4())
            file_size = content.seek(0, 2)  # 移动到文件末尾获取大小
            content.seek(0)  # 重新定位到文件开头
            
            # 计算文件哈希
            content_data = content.read()
            content.seek(0)
            file_hash = hashlib.md5(content_data).hexdigest()
            
            # 确定MIME类型
            if not content_type:
                content_type = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
            
            # 构建对象键
            file_extension = Path(file_path).suffix
            object_key = f"{datetime.now().strftime('%Y/%m/%d')}/{file_id}{file_extension}"
            
            # 准备元数据
            object_metadata = {
                "file-id": file_id,
                "original-name": Path(file_path).name,
                "file-hash": file_hash,
                "upload-time": datetime.now().isoformat(),
            }
            
            if metadata:
                object_metadata.update(metadata)
            
            # 上传文件
            await asyncio.get_event_loop().run_in_executor(
                None,
                self.minio_client.put_object,
                self.bucket_name,
                object_key,
                content,
                file_size,
                content_type,
                object_metadata
            )
            
            # 生成访问URL
            access_url = await self.generate_presigned_url(object_key, expires_in=3600)
            
            file_info = {
                "file_id": file_id,
                "object_key": object_key,
                "original_name": Path(file_path).name,
                "file_size": file_size,
                "content_type": content_type,
                "file_hash": file_hash,
                "access_url": access_url,
                "metadata": object_metadata,
                "upload_time": datetime.now().isoformat()
            }
            
            logger.info(f"文件上传成功: {file_path} -> {object_key}")
            return file_info
            
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise FileStorageException(f"文件上传失败: {str(e)}")
    
    async def upload_from_file_path(
        self, 
        local_file_path: str,
        object_key: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """从本地文件路径上传"""
        try:
            if not os.path.exists(local_file_path):
                raise FileStorageException(f"本地文件不存在: {local_file_path}")
            
            async with aiofiles.open(local_file_path, 'rb') as f:
                content = await f.read()
                
            # 创建BytesIO对象
            from io import BytesIO
            content_io = BytesIO(content)
            
            # 使用原文件名或指定的对象键
            if not object_key:
                object_key = Path(local_file_path).name
            
            return await self.upload_file(object_key, content_io, metadata=metadata)
            
        except Exception as e:
            logger.error(f"从文件路径上传失败: {str(e)}")
            raise FileStorageException(f"上传失败: {str(e)}")
    
    async def download_file(self, object_key: str, local_file_path: Optional[str] = None) -> bytes:
        """下载文件"""
        try:
            # 下载文件数据
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                self.minio_client.get_object,
                self.bucket_name,
                object_key
            )
            
            file_data = response.read()
            response.close()
            response.release_conn()
            
            # 如果指定了本地路径，保存文件
            if local_file_path:
                async with aiofiles.open(local_file_path, 'wb') as f:
                    await f.write(file_data)
                logger.debug(f"文件下载到: {local_file_path}")
            
            logger.debug(f"文件下载成功: {object_key}")
            return file_data
            
        except S3Error as e:
            if e.code == "NoSuchKey":
                raise FileStorageException(f"文件不存在: {object_key}")
            else:
                logger.error(f"文件下载失败: {str(e)}")
                raise FileStorageException(f"文件下载失败: {str(e)}")
        except Exception as e:
            logger.error(f"文件下载异常: {str(e)}")
            raise FileStorageException(f"文件下载异常: {str(e)}")
    
    async def delete_file(self, object_key: str):
        """删除文件"""
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                self.minio_client.remove_object,
                self.bucket_name,
                object_key
            )
            
            logger.info(f"文件删除成功: {object_key}")
            
        except S3Error as e:
            if e.code != "NoSuchKey":  # 忽略文件不存在的错误
                logger.error(f"文件删除失败: {str(e)}")
                raise FileStorageException(f"文件删除失败: {str(e)}")
        except Exception as e:
            logger.error(f"文件删除异常: {str(e)}")
            raise FileStorageException(f"文件删除异常: {str(e)}")
    
    async def get_file_info(self, object_key: str) -> Dict[str, Any]:
        """获取文件信息"""
        try:
            stat = await asyncio.get_event_loop().run_in_executor(
                None,
                self.minio_client.stat_object,
                self.bucket_name,
                object_key
            )
            
            return {
                "object_key": object_key,
                "file_size": stat.size,
                "content_type": stat.content_type,
                "etag": stat.etag,
                "last_modified": stat.last_modified.isoformat(),
                "metadata": stat.metadata,
                "version_id": stat.version_id
            }
            
        except S3Error as e:
            if e.code == "NoSuchKey":
                raise FileStorageException(f"文件不存在: {object_key}")
            else:
                logger.error(f"获取文件信息失败: {str(e)}")
                raise FileStorageException(f"获取文件信息失败: {str(e)}")
    
    async def generate_presigned_url(
        self, 
        object_key: str, 
        expires_in: int = 3600,
        method: str = "GET"
    ) -> str:
        """生成预签名URL"""
        try:
            from datetime import timedelta
            
            url = await asyncio.get_event_loop().run_in_executor(
                None,
                self.minio_client.presigned_get_object if method == "GET" else self.minio_client.presigned_put_object,
                self.bucket_name,
                object_key,
                timedelta(seconds=expires_in)
            )
            
            return url
            
        except Exception as e:
            logger.error(f"生成预签名URL失败: {str(e)}")
            raise FileStorageException(f"生成预签名URL失败: {str(e)}")
    
    async def list_files(
        self, 
        prefix: str = "", 
        limit: int = 100,
        start_after: str = ""
    ) -> List[Dict[str, Any]]:
        """列出文件"""
        try:
            objects = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: list(self.minio_client.list_objects(
                    self.bucket_name,
                    prefix=prefix,
                    recursive=True,
                    start_after=start_after
                ))
            )
            
            # 限制返回数量
            objects = objects[:limit] if limit > 0 else objects
            
            file_list = []
            for obj in objects:
                file_list.append({
                    "object_key": obj.object_name,
                    "file_size": obj.size,
                    "last_modified": obj.last_modified.isoformat(),
                    "etag": obj.etag,
                    "is_dir": obj.is_dir
                })
            
            return file_list
            
        except Exception as e:
            logger.error(f"列出文件失败: {str(e)}")
            raise FileStorageException(f"列出文件失败: {str(e)}")
    
    async def copy_file(self, source_key: str, dest_key: str) -> Dict[str, Any]:
        """复制文件"""
        try:
            from minio.commonconfig import CopySource
            
            copy_source = CopySource(self.bucket_name, source_key)
            
            await asyncio.get_event_loop().run_in_executor(
                None,
                self.minio_client.copy_object,
                self.bucket_name,
                dest_key,
                copy_source
            )
            
            logger.info(f"文件复制成功: {source_key} -> {dest_key}")
            
            return {
                "source_key": source_key,
                "dest_key": dest_key,
                "copy_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"文件复制失败: {str(e)}")
            raise FileStorageException(f"文件复制失败: {str(e)}")
    
    async def create_evidence_folder(self, alert_uuid: str) -> str:
        """为预警事件创建证据文件夹"""
        folder_path = f"evidence/{datetime.now().strftime('%Y/%m/%d')}/{alert_uuid}/"
        return folder_path
    
    async def save_evidence_image(
        self, 
        alert_uuid: str, 
        image_data: bytes,
        filename: str = None
    ) -> Dict[str, Any]:
        """保存证据图片"""
        try:
            if not filename:
                filename = f"screenshot_{datetime.now().strftime('%H%M%S')}.jpg"
            
            folder_path = await self.create_evidence_folder(alert_uuid)
            object_key = f"{folder_path}{filename}"
            
            from io import BytesIO
            image_io = BytesIO(image_data)
            
            result = await self.upload_file(
                object_key,
                image_io,
                content_type="image/jpeg",
                metadata={
                    "alert-uuid": alert_uuid,
                    "evidence-type": "screenshot",
                    "capture-time": datetime.now().isoformat()
                }
            )
            
            logger.info(f"证据图片保存成功: {object_key}")
            return result
            
        except Exception as e:
            logger.error(f"保存证据图片失败: {str(e)}")
            raise FileStorageException(f"保存证据图片失败: {str(e)}")
    
    async def save_evidence_video(
        self, 
        alert_uuid: str, 
        video_data: bytes,
        filename: str = None
    ) -> Dict[str, Any]:
        """保存证据视频"""
        try:
            if not filename:
                filename = f"video_{datetime.now().strftime('%H%M%S')}.mp4"
            
            folder_path = await self.create_evidence_folder(alert_uuid)
            object_key = f"{folder_path}{filename}"
            
            from io import BytesIO
            video_io = BytesIO(video_data)
            
            result = await self.upload_file(
                object_key,
                video_io,
                content_type="video/mp4",
                metadata={
                    "alert-uuid": alert_uuid,
                    "evidence-type": "video",
                    "capture-time": datetime.now().isoformat()
                }
            )
            
            logger.info(f"证据视频保存成功: {object_key}")
            return result
            
        except Exception as e:
            logger.error(f"保存证据视频失败: {str(e)}")
            raise FileStorageException(f"保存证据视频失败: {str(e)}")
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            # 检查存储桶是否可访问
            self.minio_client.bucket_exists(self.bucket_name)
            return True
        except Exception as e:
            logger.error(f"存储服务健康检查失败: {str(e)}")
            return False
    
    async def cleanup_expired_files(self, days: int = 30):
        """清理过期文件"""
        try:
            # 计算过期时间
            expire_date = datetime.now() - timedelta(days=days)
            
            # 列出所有文件
            objects = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: list(self.minio_client.list_objects(
                    self.bucket_name,
                    recursive=True
                ))
            )
            
            expired_count = 0
            for obj in objects:
                if obj.last_modified < expire_date:
                    await self.delete_file(obj.object_name)
                    expired_count += 1
            
            logger.info(f"清理了 {expired_count} 个过期文件")
            return expired_count
            
        except Exception as e:
            logger.error(f"清理过期文件失败: {str(e)}")
            raise FileStorageException(f"清理过期文件失败: {str(e)}")


# 全局文件存储服务实例
file_storage_service = FileStorageService() 