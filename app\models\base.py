"""
SQLAlchemy基础模型
"""
from datetime import datetime
from typing import Any, Dict, Optional

from sqlalchemy import <PERSON>umn, Integer, DateTime, Boolean, String, text, BigInteger
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import AsyncAttrs
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column

class Base(AsyncAttrs, DeclarativeBase):
    """SQLAlchemy基础模型类"""
    pass

class BaseModel(Base):
    """带有公共字段的基础模型"""
    __abstract__ = True
    
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, index=True, comment="主键ID")

    # 审计字段
    creator: Mapped[str] = mapped_column(String(64), default="", comment="创建者")
    create_time: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.now,
        server_default=text("CURRENT_TIMESTAMP"),
        comment="创建时间"
    )
    updater: Mapped[str] = mapped_column(String(64), default="", comment="更新者")
    update_time: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.now,
        onupdate=datetime.now,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间"
    )

    # 软删除
    deleted: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        server_default=text("b'0'"),
        comment="是否删除"
    )

    # 多租户
    tenant_id: Mapped[int] = mapped_column(BigInteger, default=0, comment="租户编号")

    # 兼容性属性
    @property
    def created_at(self) -> datetime:
        """兼容性属性：创建时间"""
        return self.create_time

    @property
    def updated_at(self) -> datetime:
        """兼容性属性：更新时间"""
        return self.update_time

    @property
    def is_deleted(self) -> bool:
        """兼容性属性：是否删除"""
        return self.deleted
    
    def to_dict(self, exclude_fields: Optional[list] = None) -> Dict[str, Any]:
        """转换为字典"""
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                if isinstance(value, datetime):
                    result[column.name] = value.isoformat()
                else:
                    result[column.name] = value
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseModel':
        """从字典创建实例"""
        return cls(**data)
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id})>" 