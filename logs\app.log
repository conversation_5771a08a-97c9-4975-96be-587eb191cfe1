2025-07-21 13:38:40 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:38:42 - app.services.storage.file_storage_service - ERROR - 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA07B15D6A6, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:38:42 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA07B15D6A6, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:39:10 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:39:10 - app.services.storage.file_storage_service - ERROR - 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA7156CE885, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:39:10 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA7156CE885, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:39:10 - __main__ - ERROR - 启动失败: [6000] 存储服务初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA7156CE885, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:45:53 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:46:19 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:46:19 - app.services.storage.file_storage_service - ERROR - 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542E0B00E5DB02, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:46:19 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542E0B00E5DB02, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:46:19 - app.services.storage.file_storage_service - WARNING - ⚠️ 降级到本地文件存储
2025-07-21 13:46:19 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 13:46:19 - __main__ - ERROR - 启动失败: AlertEventRepository.__init__() missing 1 required positional argument: 'db'
2025-07-21 13:51:47 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:51:48 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 13:51:48 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 13:51:48 - __main__ - ERROR - 启动失败: AlertEventRepository.__init__() missing 1 required positional argument: 'db'
2025-07-21 14:01:13 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:01:14 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 14:01:14 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 14:01:14 - __main__ - ERROR - 启动失败: AlertService.__init__() missing 1 required positional argument: 'db_session'
2025-07-21 14:18:14 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:19:24 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:19:24 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 14:19:24 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 14:19:44 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:19:45 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 14:19:45 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 14:19:45 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 14:19:45 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 14:19:45 - __main__ - ERROR - 启动失败: no running event loop
2025-07-21 14:52:09 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:52:10 - app.services.storage.file_storage_service - ERROR - 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 185431A2BEC9E42F, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 14:52:10 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 185431A2BEC9E42F, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 14:52:10 - __main__ - ERROR - 启动失败: [6000] 存储服务初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 185431A2BEC9E42F, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 14:59:22 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:59:23 - __main__ - ERROR - 启动失败: AlertEventRepository.__init__() missing 1 required positional argument: 'db'
