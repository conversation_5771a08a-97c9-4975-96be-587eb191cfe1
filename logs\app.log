2025-07-21 13:38:40 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:38:42 - app.services.storage.file_storage_service - ERROR - 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA07B15D6A6, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:38:42 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA07B15D6A6, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:39:10 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:39:10 - app.services.storage.file_storage_service - ERROR - 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA7156CE885, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:39:10 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA7156CE885, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:39:10 - __main__ - ERROR - 启动失败: [6000] 存储服务初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA7156CE885, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:45:53 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:46:19 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:46:19 - app.services.storage.file_storage_service - ERROR - 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542E0B00E5DB02, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:46:19 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542E0B00E5DB02, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:46:19 - app.services.storage.file_storage_service - WARNING - ⚠️ 降级到本地文件存储
2025-07-21 13:46:19 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 13:46:19 - __main__ - ERROR - 启动失败: AlertEventRepository.__init__() missing 1 required positional argument: 'db'
2025-07-21 13:51:47 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:51:48 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 13:51:48 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 13:51:48 - __main__ - ERROR - 启动失败: AlertEventRepository.__init__() missing 1 required positional argument: 'db'
2025-07-21 14:01:13 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:01:14 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 14:01:14 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 14:01:14 - __main__ - ERROR - 启动失败: AlertService.__init__() missing 1 required positional argument: 'db_session'
2025-07-21 14:18:14 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:19:24 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:19:24 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 14:19:24 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 14:19:44 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:19:45 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 14:19:45 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 14:19:45 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 14:19:45 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 14:19:45 - __main__ - ERROR - 启动失败: no running event loop
2025-07-21 14:52:09 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:52:10 - app.services.storage.file_storage_service - ERROR - 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 185431A2BEC9E42F, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 14:52:10 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 185431A2BEC9E42F, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 14:52:10 - __main__ - ERROR - 启动失败: [6000] 存储服务初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 185431A2BEC9E42F, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 14:59:22 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:59:23 - __main__ - ERROR - 启动失败: AlertEventRepository.__init__() missing 1 required positional argument: 'db'
2025-07-21 15:05:54 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 15:05:55 - __main__ - ERROR - 启动失败: AlertService.__init__() missing 1 required positional argument: 'db_session'
2025-07-21 15:12:54 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 15:12:55 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 15:12:55 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 15:12:55 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 15:12:55 - app.core.cache - ERROR - ❌ Redis缓存连接失败: Error 22 connecting to 0.0.0.0:8000. 22.
2025-07-21 15:12:55 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 15:12:55 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 15:12:55 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 15:12:55 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 15:12:55 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 15:12:55 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 15:12:55 - app.main - ERROR - ❌ 系统启动失败: local variable 'get_session' referenced before assignment
2025-07-21 15:12:55 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 15:12:55 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 15:12:55 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 15:23:57 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 15:23:58 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 15:23:58 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 15:23:58 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 15:24:00 - app.core.cache - INFO - ✅ Redis缓存连接成功
2025-07-21 15:24:00 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 15:24:00 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 15:24:00 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 15:24:00 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 15:24:00 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 15:24:00 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 15:24:00 - app.main - ERROR - ❌ 系统启动失败: object async_generator can't be used in 'await' expression
2025-07-21 15:24:00 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 15:24:00 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 15:24:00 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 15:32:43 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 15:32:44 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 15:32:44 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 15:32:44 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 15:32:46 - app.core.cache - INFO - ✅ Redis缓存连接成功
2025-07-21 15:32:46 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 15:32:46 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 15:32:46 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 15:32:46 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 15:32:46 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 15:32:46 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 15:32:46 - app.main - INFO - 🤖 初始化AI分析器...
2025-07-21 15:32:46 - app.main - INFO - 📹 初始化摄像头管理器...
2025-07-21 15:32:46 - app.main - ERROR - ❌ 系统启动失败: 'NoneType' object has no attribute 'add_config_change_listener'
2025-07-21 15:32:46 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 15:32:46 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 15:32:46 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 15:32:46 - app.services.alert.alert_service - INFO - 预警服务已关闭
2025-07-21 17:17:50 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 17:17:51 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 17:17:51 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 17:17:51 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 17:17:53 - app.core.cache - INFO - ✅ Redis缓存连接成功
2025-07-21 17:17:53 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 17:17:53 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 17:17:53 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 17:17:53 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 17:17:53 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 17:17:53 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 17:17:53 - app.main - INFO - 🤖 初始化AI分析器...
2025-07-21 17:17:53 - app.main - INFO - 📹 初始化摄像头管理器...
2025-07-21 17:17:53 - app.services.camera.camera_manager - WARNING - 配置管理器未提供，将跳过配置变更监听
2025-07-21 17:17:53 - app.services.camera.camera_manager - INFO - 摄像头管理器初始化完成，最大支持 100 路摄像头
2025-07-21 17:17:53 - app.services.camera.camera_manager - ERROR - 加载摄像头配置失败: __aenter__
2025-07-21 17:17:53 - app.services.camera.camera_manager - INFO - 摄像头管理器已启动
2025-07-21 17:17:53 - app.main - INFO - 🔄 初始化视频处理管道...
2025-07-21 17:17:53 - app.services.video.video_pipeline - INFO - 视频分析管道初始化完成
2025-07-21 17:17:53 - app.services.video.video_pipeline - INFO - 管道状态变更: stopped -> starting
2025-07-21 17:17:53 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 17:17:53 - app.services.video.video_pipeline - ERROR - 初始化管道组件失败: 'MotionDetectionManager' object has no attribute 'start'
2025-07-21 17:17:53 - app.services.video.video_pipeline - INFO - 管道状态变更: starting -> error
2025-07-21 17:17:53 - app.services.video.video_pipeline - ERROR - 启动视频分析管道失败: 'MotionDetectionManager' object has no attribute 'start'
2025-07-21 17:17:53 - app.main - ERROR - ❌ 系统启动失败: [3000] 启动管道失败: 'MotionDetectionManager' object has no attribute 'start'
2025-07-21 17:17:53 - app.services.camera.camera_manager - INFO - 摄像头重连任务已启动
2025-07-21 17:17:53 - app.services.camera.camera_manager - INFO - 摄像头健康检查任务已启动
2025-07-21 17:17:53 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 17:17:53 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 17:17:53 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 17:17:53 - app.services.alert.alert_service - INFO - 预警服务已关闭
2025-07-21 17:17:53 - app.services.camera.camera_manager - INFO - 所有摄像头流已停止
2025-07-21 17:17:53 - app.services.camera.camera_manager - INFO - 摄像头管理器已停止
2025-07-21 17:27:15 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 17:27:15 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 17:27:15 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 17:27:15 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 17:27:18 - app.core.cache - INFO - ✅ Redis缓存连接成功
2025-07-21 17:27:18 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 17:27:18 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 17:27:18 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 17:27:18 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 17:27:18 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 17:27:18 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 17:27:18 - app.main - INFO - 🤖 初始化AI分析器...
2025-07-21 17:27:18 - app.main - INFO - 📹 初始化摄像头管理器...
2025-07-21 17:27:18 - app.services.camera.camera_manager - WARNING - 配置管理器未提供，将跳过配置变更监听
2025-07-21 17:27:18 - app.services.camera.camera_manager - INFO - 摄像头管理器初始化完成，最大支持 100 路摄像头
2025-07-21 17:27:18 - app.services.camera.camera_manager - ERROR - 加载摄像头配置失败: __aenter__
2025-07-21 17:27:18 - app.services.camera.camera_manager - INFO - 摄像头管理器已启动
2025-07-21 17:27:18 - app.main - INFO - 🔄 初始化视频处理管道...
2025-07-21 17:27:18 - app.services.video.video_pipeline - INFO - 视频分析管道初始化完成
2025-07-21 17:27:18 - app.services.video.video_pipeline - INFO - 管道状态变更: stopped -> starting
2025-07-21 17:27:18 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 17:27:18 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 17:27:18 - app.services.video.pre_filter - INFO - 预过滤处理器已初始化
2025-07-21 17:27:18 - app.services.video.video_pipeline - ERROR - 初始化管道组件失败: MIXED
2025-07-21 17:27:18 - app.services.video.video_pipeline - INFO - 管道状态变更: starting -> error
2025-07-21 17:27:18 - app.services.video.video_pipeline - ERROR - 启动视频分析管道失败: MIXED
2025-07-21 17:27:18 - app.main - ERROR - ❌ 系统启动失败: [3000] 启动管道失败: MIXED
2025-07-21 17:27:18 - app.services.camera.camera_manager - INFO - 摄像头重连任务已启动
2025-07-21 17:27:18 - app.services.camera.camera_manager - INFO - 摄像头健康检查任务已启动
2025-07-21 17:27:18 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 17:27:18 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 17:27:18 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 17:27:18 - app.services.alert.alert_service - INFO - 预警服务已关闭
2025-07-21 17:27:18 - app.services.camera.camera_manager - INFO - 所有摄像头流已停止
2025-07-21 17:27:18 - app.services.camera.camera_manager - INFO - 摄像头管理器已停止
2025-07-21 17:31:11 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 17:31:12 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 17:31:12 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 17:31:12 - app.database.engine - INFO - 创建数据库引擎...
2025-07-21 17:31:12 - app.database.engine - INFO - ✅ 数据库引擎创建成功
2025-07-21 17:31:12 - app.database.connection - INFO - ✅ 数据库会话工厂初始化完成
2025-07-21 17:31:12 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 17:31:14 - app.core.cache - INFO - ✅ Redis缓存连接成功
2025-07-21 17:31:14 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 17:31:14 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 17:31:14 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 17:31:14 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 17:31:14 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 17:31:14 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 17:31:14 - app.main - INFO - 🤖 初始化AI分析器...
2025-07-21 17:31:14 - app.main - INFO - 📹 初始化摄像头管理器...
2025-07-21 17:31:14 - app.services.camera.camera_manager - WARNING - 配置管理器未提供，将跳过配置变更监听
2025-07-21 17:31:14 - app.services.camera.camera_manager - INFO - 摄像头管理器初始化完成，最大支持 100 路摄像头
2025-07-21 17:31:14 - app.services.camera.camera_manager - ERROR - 加载摄像头配置失败: __aenter__
2025-07-21 17:31:14 - app.services.camera.camera_manager - INFO - 摄像头管理器已启动
2025-07-21 17:31:14 - app.main - INFO - 🔄 初始化视频处理管道...
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 视频分析管道初始化完成
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 管道状态变更: stopped -> starting
2025-07-21 17:31:14 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 17:31:14 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 17:31:14 - app.services.video.pre_filter - INFO - 预过滤处理器已初始化
2025-07-21 17:31:14 - app.services.video.frame_aggregator - INFO - 帧聚合器已初始化
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - AI分析队列管理器已初始化
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - AI集成服务已初始化
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - AI集成服务已启动，工作器数量: 6
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 管道组件初始化完成
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 已启动 4 个处理工作器
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 后台任务已启动
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 管道状态变更: starting -> running
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 视频分析管道已启动
2025-07-21 17:31:14 - app.main - INFO - 📺 初始化视频管理器...
2025-07-21 17:31:14 - app.main - ERROR - ❌ 系统启动失败: VideoManager.__init__() got an unexpected keyword argument 'frame_buffer_manager'
2025-07-21 17:31:14 - app.database.engine - INFO - 关闭数据库引擎...
2025-07-21 17:31:14 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 20  Connections in pool: 0 Current Overflow: -20 Current Checked out connections: 0
2025-07-21 17:31:14 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-07-21 17:31:14 - app.database.engine - INFO - ✅ 数据库引擎已关闭
2025-07-21 17:31:14 - app.database.connection - INFO - ✅ 数据库连接已关闭
2025-07-21 17:31:14 - app.services.camera.camera_manager - INFO - 摄像头重连任务已启动
2025-07-21 17:31:14 - app.services.camera.camera_manager - INFO - 摄像头健康检查任务已启动
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-0
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-1
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-2
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-3
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - 批量处理器已启动
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - 清理工作器已启动
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理工作器 worker-0 已启动
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理工作器 worker-1 已启动
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理工作器 worker-2 已启动
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理工作器 worker-3 已启动
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 统计任务已启动
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 健康检查任务已启动
2025-07-21 17:31:14 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 17:31:14 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 17:31:14 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 17:31:14 - app.services.alert.alert_service - INFO - 预警服务已关闭
2025-07-21 17:31:14 - app.services.camera.camera_manager - INFO - 所有摄像头流已停止
2025-07-21 17:31:14 - app.services.camera.camera_manager - INFO - 摄像头管理器已停止
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 管道状态变更: running -> stopping
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 后台任务已停止
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理工作器 worker-0 被取消
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理工作器 worker-0 已退出
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理工作器 worker-1 被取消
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理工作器 worker-1 已退出
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理工作器 worker-2 被取消
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理工作器 worker-2 已退出
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理工作器 worker-3 被取消
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理工作器 worker-3 已退出
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 处理任务已停止
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-0
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-1
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-2
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-3
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - 批量处理器已停止
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - 清理工作器已停止
2025-07-21 17:31:14 - app.services.video.ai_integration - INFO - AI集成服务已停止
2025-07-21 17:31:14 - app.services.video.video_pipeline - ERROR - 停止组件失败: 'FrameAggregator' object has no attribute 'stop'
2025-07-21 17:31:14 - app.services.video.video_pipeline - ERROR - 停止组件失败: 'PreFilterProcessor' object has no attribute 'stop'
2025-07-21 17:31:14 - app.services.video.video_pipeline - ERROR - 停止组件失败: 'MotionDetectionManager' object has no attribute 'stop'
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 管道组件清理完成
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 管道状态变更: stopping -> stopped
2025-07-21 17:31:14 - app.services.video.video_pipeline - INFO - 视频分析管道已停止
2025-07-21 17:51:39 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 17:51:40 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 17:51:40 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 17:51:40 - app.database.engine - INFO - 创建数据库引擎...
2025-07-21 17:51:40 - app.database.engine - INFO - ✅ 数据库引擎创建成功
2025-07-21 17:51:40 - app.database.connection - INFO - ✅ 数据库会话工厂初始化完成
2025-07-21 17:51:40 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 17:51:42 - app.core.cache - INFO - ✅ Redis缓存连接成功
2025-07-21 17:51:42 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 17:51:42 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 17:51:42 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 17:51:42 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 17:51:42 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 17:51:42 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 17:51:42 - app.main - INFO - 🤖 初始化AI分析器...
2025-07-21 17:51:42 - app.main - INFO - 📹 初始化摄像头管理器...
2025-07-21 17:51:42 - app.services.camera.camera_manager - WARNING - 配置管理器未提供，将跳过配置变更监听
2025-07-21 17:51:42 - app.services.camera.camera_manager - INFO - 摄像头管理器初始化完成，最大支持 100 路摄像头
2025-07-21 17:51:42 - app.services.camera.camera_manager - WARNING - 加载摄像头配置失败: __aenter__，系统将继续启动
2025-07-21 17:51:42 - app.services.camera.camera_manager - INFO - 摄像头管理器已启动
2025-07-21 17:51:42 - app.main - INFO - 🔄 初始化视频处理管道...
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 视频分析管道初始化完成
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 管道状态变更: stopped -> starting
2025-07-21 17:51:42 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 17:51:42 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 17:51:42 - app.services.video.pre_filter - INFO - 预过滤处理器已初始化
2025-07-21 17:51:42 - app.services.video.frame_aggregator - INFO - 帧聚合器已初始化
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - AI分析队列管理器已初始化
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - AI集成服务已初始化
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - AI集成服务已启动，工作器数量: 6
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 管道组件初始化完成
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 已启动 4 个处理工作器
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 后台任务已启动
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 管道状态变更: starting -> running
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 视频分析管道已启动
2025-07-21 17:51:42 - app.main - INFO - 📺 初始化视频管理器...
2025-07-21 17:51:42 - app.main - ERROR - ❌ 系统启动失败: __aenter__
2025-07-21 17:51:42 - app.database.engine - INFO - 关闭数据库引擎...
2025-07-21 17:51:42 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 20  Connections in pool: 0 Current Overflow: -20 Current Checked out connections: 0
2025-07-21 17:51:42 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-07-21 17:51:42 - app.database.engine - INFO - ✅ 数据库引擎已关闭
2025-07-21 17:51:42 - app.database.connection - INFO - ✅ 数据库连接已关闭
2025-07-21 17:51:42 - app.services.camera.camera_manager - INFO - 摄像头重连任务已启动
2025-07-21 17:51:42 - app.services.camera.camera_manager - INFO - 摄像头健康检查任务已启动
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-0
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-1
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-2
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-3
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - 批量处理器已启动
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - 清理工作器已启动
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理工作器 worker-0 已启动
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理工作器 worker-1 已启动
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理工作器 worker-2 已启动
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理工作器 worker-3 已启动
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 统计任务已启动
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 健康检查任务已启动
2025-07-21 17:51:42 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 17:51:42 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 17:51:42 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 17:51:42 - app.services.alert.alert_service - INFO - 预警服务已关闭
2025-07-21 17:51:42 - app.services.camera.camera_manager - INFO - 所有摄像头流已停止
2025-07-21 17:51:42 - app.services.camera.camera_manager - INFO - 摄像头管理器已停止
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 管道状态变更: running -> stopping
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 后台任务已停止
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理工作器 worker-0 被取消
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理工作器 worker-0 已退出
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理工作器 worker-1 被取消
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理工作器 worker-1 已退出
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理工作器 worker-2 被取消
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理工作器 worker-2 已退出
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理工作器 worker-3 被取消
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理工作器 worker-3 已退出
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 处理任务已停止
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-0
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-1
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-2
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-3
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - 批量处理器已停止
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - 清理工作器已停止
2025-07-21 17:51:42 - app.services.video.ai_integration - INFO - AI集成服务已停止
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 管道组件清理完成
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 管道状态变更: stopping -> stopped
2025-07-21 17:51:42 - app.services.video.video_pipeline - INFO - 视频分析管道已停止
2025-07-21 18:01:00 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 18:01:01 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 18:01:01 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 18:01:01 - app.database.engine - INFO - 创建数据库引擎...
2025-07-21 18:01:01 - app.database.engine - INFO - ✅ 数据库引擎创建成功
2025-07-21 18:01:01 - app.database.connection - INFO - ✅ 数据库会话工厂初始化完成
2025-07-21 18:01:01 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 18:01:03 - app.core.cache - INFO - ✅ Redis缓存连接成功
2025-07-21 18:01:03 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 18:01:03 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 18:01:03 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 18:01:03 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 18:01:03 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 18:01:03 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 18:01:03 - app.main - INFO - 🤖 初始化AI分析器...
2025-07-21 18:01:03 - app.main - INFO - 📹 初始化摄像头管理器...
2025-07-21 18:01:03 - app.services.camera.camera_manager - WARNING - 配置管理器未提供，将跳过配置变更监听
2025-07-21 18:01:03 - app.services.camera.camera_manager - INFO - 摄像头管理器初始化完成，最大支持 100 路摄像头
2025-07-21 18:01:03 - app.services.camera.camera_manager - INFO - 摄像头重连任务已启动
2025-07-21 18:01:03 - app.services.camera.camera_manager - INFO - 摄像头健康检查任务已启动
2025-07-21 18:01:03 - app.database.connection - ERROR - 数据库操作异常: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '0.0.0.0'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-21 18:01:03 - app.services.camera.camera_manager - WARNING - 加载摄像头配置失败: [2001] 数据库操作失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '0.0.0.0'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)，系统将继续启动
2025-07-21 18:01:03 - app.services.camera.camera_manager - INFO - 摄像头管理器已启动
2025-07-21 18:01:03 - app.main - INFO - 🔄 初始化视频处理管道...
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 视频分析管道初始化完成
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 管道状态变更: stopped -> starting
2025-07-21 18:01:03 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 18:01:03 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 18:01:03 - app.services.video.pre_filter - INFO - 预过滤处理器已初始化
2025-07-21 18:01:03 - app.services.video.frame_aggregator - INFO - 帧聚合器已初始化
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - AI分析队列管理器已初始化
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - AI集成服务已初始化
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - AI集成服务已启动，工作器数量: 6
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 管道组件初始化完成
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 已启动 4 个处理工作器
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 后台任务已启动
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 管道状态变更: starting -> running
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 视频分析管道已启动
2025-07-21 18:01:03 - app.main - INFO - 📺 初始化视频管理器...
2025-07-21 18:01:03 - app.database.connection - ERROR - 未知数据库异常: ConfigService.__init__() missing 5 required positional arguments: 'camera_repo', 'analysis_rule_repo', 'anomaly_behavior_repo', 'prompt_template_repo', and 'rule_behavior_mapping_repo'
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-0
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-1
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-2
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-3
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - 批量处理器已启动
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - 清理工作器已启动
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理工作器 worker-0 已启动
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理工作器 worker-1 已启动
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理工作器 worker-2 已启动
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理工作器 worker-3 已启动
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 统计任务已启动
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 健康检查任务已启动
2025-07-21 18:01:03 - app.main - ERROR - ❌ 系统启动失败: [2001] 数据库操作异常: ConfigService.__init__() missing 5 required positional arguments: 'camera_repo', 'analysis_rule_repo', 'anomaly_behavior_repo', 'prompt_template_repo', and 'rule_behavior_mapping_repo'
2025-07-21 18:01:03 - app.database.engine - INFO - 关闭数据库引擎...
2025-07-21 18:01:03 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 20  Connections in pool: 0 Current Overflow: -20 Current Checked out connections: 0
2025-07-21 18:01:03 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-07-21 18:01:03 - app.database.engine - INFO - ✅ 数据库引擎已关闭
2025-07-21 18:01:03 - app.database.connection - INFO - ✅ 数据库连接已关闭
2025-07-21 18:01:03 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 18:01:03 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 18:01:03 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 18:01:03 - app.services.alert.alert_service - INFO - 预警服务已关闭
2025-07-21 18:01:03 - app.services.camera.camera_manager - INFO - 所有摄像头流已停止
2025-07-21 18:01:03 - app.services.camera.camera_manager - INFO - 摄像头管理器已停止
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 管道状态变更: running -> stopping
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 后台任务已停止
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理工作器 worker-0 被取消
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理工作器 worker-0 已退出
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理工作器 worker-1 被取消
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理工作器 worker-1 已退出
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理工作器 worker-2 被取消
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理工作器 worker-2 已退出
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理工作器 worker-3 被取消
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理工作器 worker-3 已退出
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 处理任务已停止
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-0
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-1
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-2
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-3
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - 批量处理器已停止
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - 清理工作器已停止
2025-07-21 18:01:03 - app.services.video.ai_integration - INFO - AI集成服务已停止
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 管道组件清理完成
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 管道状态变更: stopping -> stopped
2025-07-21 18:01:03 - app.services.video.video_pipeline - INFO - 视频分析管道已停止
2025-07-21 18:06:24 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 18:06:25 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 18:06:25 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 18:06:25 - app.database.engine - INFO - 创建数据库引擎...
2025-07-21 18:06:25 - app.database.engine - INFO - ✅ 数据库引擎创建成功
2025-07-21 18:06:25 - app.database.connection - INFO - ✅ 数据库会话工厂初始化完成
2025-07-21 18:06:25 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 18:06:27 - app.core.cache - INFO - ✅ Redis缓存连接成功
2025-07-21 18:06:27 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 18:06:27 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 18:06:27 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 18:06:27 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 18:06:27 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 18:06:27 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 18:06:27 - app.main - INFO - 🤖 初始化AI分析器...
2025-07-21 18:06:27 - app.main - INFO - 📹 初始化摄像头管理器...
2025-07-21 18:06:27 - app.services.camera.camera_manager - WARNING - 配置管理器未提供，将跳过配置变更监听
2025-07-21 18:06:27 - app.services.camera.camera_manager - INFO - 摄像头管理器初始化完成，最大支持 100 路摄像头
2025-07-21 18:06:27 - app.services.camera.camera_manager - INFO - 摄像头重连任务已启动
2025-07-21 18:06:27 - app.services.camera.camera_manager - INFO - 摄像头健康检查任务已启动
2025-07-21 18:06:27 - app.database.connection - ERROR - 数据库操作异常: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '0.0.0.0'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-21 18:06:27 - app.services.camera.camera_manager - WARNING - 加载摄像头配置失败: [2001] 数据库操作失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on '0.0.0.0'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)，系统将继续启动
2025-07-21 18:06:27 - app.services.camera.camera_manager - INFO - 摄像头管理器已启动
2025-07-21 18:06:27 - app.main - INFO - 🔄 初始化视频处理管道...
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 视频分析管道初始化完成
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 管道状态变更: stopped -> starting
2025-07-21 18:06:27 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 18:06:27 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 18:06:27 - app.services.video.pre_filter - INFO - 预过滤处理器已初始化
2025-07-21 18:06:27 - app.services.video.frame_aggregator - INFO - 帧聚合器已初始化
2025-07-21 18:06:27 - app.services.video.ai_integration - INFO - AI分析队列管理器已初始化
2025-07-21 18:06:27 - app.services.video.ai_integration - INFO - AI集成服务已初始化
2025-07-21 18:06:27 - app.services.video.ai_integration - INFO - AI集成服务已启动，工作器数量: 6
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 管道组件初始化完成
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 已启动 4 个处理工作器
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 后台任务已启动
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 管道状态变更: starting -> running
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 视频分析管道已启动
2025-07-21 18:06:27 - app.main - INFO - 📺 初始化视频管理器...
2025-07-21 18:06:27 - app.core.logger - INFO - 配置服务初始化完成
2025-07-21 18:06:27 - app.core.logger - INFO - 帧调度器初始化完成
2025-07-21 18:06:27 - app.core.logger - INFO - 视频管理器初始化完成
2025-07-21 18:06:27 - app.main - INFO - ✅ 视频管理器初始化完成
2025-07-21 18:06:27 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-0
2025-07-21 18:06:27 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-1
2025-07-21 18:06:27 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-2
2025-07-21 18:06:27 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-3
2025-07-21 18:06:27 - app.services.video.ai_integration - INFO - 批量处理器已启动
2025-07-21 18:06:27 - app.services.video.ai_integration - INFO - 清理工作器已启动
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 处理工作器 worker-0 已启动
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 处理工作器 worker-1 已启动
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 处理工作器 worker-2 已启动
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 处理工作器 worker-3 已启动
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 统计任务已启动
2025-07-21 18:06:27 - app.services.video.video_pipeline - INFO - 健康检查任务已启动
2025-07-21 18:06:27 - app.main - INFO - ✅ 系统启动完成
2025-07-21 18:06:27 - app.main - INFO - 📊 配置缓存: 暂时禁用（简化模式）
2025-07-21 18:06:27 - app.main - INFO - 📹 摄像头管理: 最大支持 100 路
2025-07-21 18:06:27 - app.main - INFO - 🔄 视频管道: 三层优化架构已启用
2025-07-21 18:06:57 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:06:57 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:06:57 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:06:57 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:07:27 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:07:27 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:07:27 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:07:27 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:07:34 - app.main - INFO - 🛑 系统关闭中...
2025-07-21 18:07:34 - app.main - INFO - 停止服务: video_pipeline
2025-07-21 18:07:34 - app.services.video.video_pipeline - INFO - 管道状态变更: running -> stopping
2025-07-21 18:07:34 - app.services.video.video_pipeline - INFO - 处理工作器 worker-1 已退出
2025-07-21 18:07:34 - app.services.video.video_pipeline - INFO - 处理工作器 worker-0 已退出
2025-07-21 18:07:34 - app.services.video.video_pipeline - INFO - 处理工作器 worker-2 已退出
2025-07-21 18:07:34 - app.services.video.video_pipeline - INFO - 处理工作器 worker-3 已退出
2025-07-21 18:07:34 - app.services.video.video_pipeline - INFO - 后台任务已停止
2025-07-21 18:07:34 - app.services.video.video_pipeline - INFO - 处理任务已停止
2025-07-21 18:07:34 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-0
2025-07-21 18:07:34 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-1
2025-07-21 18:07:34 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-2
2025-07-21 18:07:34 - app.services.video.ai_integration - INFO - 分析工作器已停止: worker-3
2025-07-21 18:07:34 - app.services.video.ai_integration - INFO - 批量处理器已停止
2025-07-21 18:07:34 - app.services.video.ai_integration - INFO - 清理工作器已停止
2025-07-21 18:07:34 - app.services.video.ai_integration - INFO - AI集成服务已停止
2025-07-21 18:07:34 - app.services.video.video_pipeline - INFO - 管道组件清理完成
2025-07-21 18:07:34 - app.services.video.video_pipeline - INFO - 管道状态变更: stopping -> stopped
2025-07-21 18:07:34 - app.services.video.video_pipeline - INFO - 视频分析管道已停止
2025-07-21 18:07:34 - app.main - INFO - 停止服务: camera_manager
2025-07-21 18:07:34 - app.services.camera.camera_manager - INFO - 所有摄像头流已停止
2025-07-21 18:07:34 - app.services.camera.camera_manager - INFO - 摄像头管理器已停止
2025-07-21 18:07:34 - app.main - INFO - 停止服务: video_manager
2025-07-21 18:07:34 - app.main - INFO - 停止服务: ai_analyzer
2025-07-21 18:07:34 - app.main - INFO - 停止服务: alert_service
2025-07-21 18:07:34 - app.services.alert.alert_service - INFO - 预警服务已关闭
2025-07-21 18:07:34 - app.main - INFO - 停止服务: websocket_service
2025-07-21 18:07:34 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 18:07:34 - app.main - INFO - 停止服务: storage_service
2025-07-21 18:07:34 - app.main - INFO - 停止服务: frame_buffer_manager
2025-07-21 18:07:34 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 18:07:34 - app.main - INFO - 停止服务: configuration_manager
2025-07-21 18:07:34 - app.main - INFO - 停止服务: config_service
2025-07-21 18:07:34 - app.main - INFO - 停止服务: redis_cache
2025-07-21 18:07:34 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 18:07:34 - app.main - INFO - 📊 关闭数据库连接...
2025-07-21 18:07:34 - app.database.engine - INFO - 关闭数据库引擎...
2025-07-21 18:07:34 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 20  Connections in pool: 0 Current Overflow: -20 Current Checked out connections: 0
2025-07-21 18:07:34 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-07-21 18:07:34 - app.database.engine - INFO - ✅ 数据库引擎已关闭
2025-07-21 18:07:34 - app.database.connection - INFO - ✅ 数据库连接已关闭
2025-07-21 18:07:34 - app.main - INFO - ✅ 系统关闭完成
2025-07-21 18:10:01 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 18:10:02 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 18:10:02 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 18:10:02 - app.database.engine - INFO - 创建数据库引擎...
2025-07-21 18:10:02 - app.database.engine - INFO - ✅ 数据库引擎创建成功
2025-07-21 18:10:02 - app.database.connection - INFO - ✅ 数据库会话工厂初始化完成
2025-07-21 18:10:02 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 18:10:04 - app.core.cache - INFO - ✅ Redis缓存连接成功
2025-07-21 18:10:04 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 18:10:04 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 18:10:04 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 18:10:04 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 18:10:04 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 18:10:04 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 18:10:04 - app.main - INFO - 🤖 初始化AI分析器...
2025-07-21 18:10:04 - app.main - INFO - 📹 初始化摄像头管理器...
2025-07-21 18:10:04 - app.services.camera.camera_manager - WARNING - 配置管理器未提供，将跳过配置变更监听
2025-07-21 18:10:04 - app.services.camera.camera_manager - INFO - 摄像头管理器初始化完成，最大支持 100 路摄像头
2025-07-21 18:10:04 - app.services.camera.camera_manager - INFO - 摄像头重连任务已启动
2025-07-21 18:10:04 - app.services.camera.camera_manager - INFO - 摄像头健康检查任务已启动
2025-07-21 18:10:04 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-07-21 18:10:04 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-21 18:10:04 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-07-21 18:10:04 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-21 18:10:04 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-07-21 18:10:04 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-21 18:10:04 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-21 18:10:04 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-21 18:10:04 - app.database.connection - ERROR - 数据库操作异常: Mapper 'Mapper[Camera(cameras)]' has no property 'evidence_files'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-21 18:10:04 - app.services.camera.camera_manager - WARNING - 加载摄像头配置失败: [2001] 数据库操作失败: Mapper 'Mapper[Camera(cameras)]' has no property 'evidence_files'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.，系统将继续启动
2025-07-21 18:10:04 - app.services.camera.camera_manager - INFO - 摄像头管理器已启动
2025-07-21 18:10:04 - app.main - INFO - 🔄 初始化视频处理管道...
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 视频分析管道初始化完成
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 管道状态变更: stopped -> starting
2025-07-21 18:10:04 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 18:10:04 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 18:10:04 - app.services.video.pre_filter - INFO - 预过滤处理器已初始化
2025-07-21 18:10:04 - app.services.video.frame_aggregator - INFO - 帧聚合器已初始化
2025-07-21 18:10:04 - app.services.video.ai_integration - INFO - AI分析队列管理器已初始化
2025-07-21 18:10:04 - app.services.video.ai_integration - INFO - AI集成服务已初始化
2025-07-21 18:10:04 - app.services.video.ai_integration - INFO - AI集成服务已启动，工作器数量: 6
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 管道组件初始化完成
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 已启动 4 个处理工作器
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 后台任务已启动
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 管道状态变更: starting -> running
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 视频分析管道已启动
2025-07-21 18:10:04 - app.main - INFO - 📺 初始化视频管理器...
2025-07-21 18:10:04 - app.core.logger - INFO - 配置服务初始化完成
2025-07-21 18:10:04 - app.core.logger - INFO - 帧调度器初始化完成
2025-07-21 18:10:04 - app.core.logger - INFO - 视频管理器初始化完成
2025-07-21 18:10:04 - app.main - INFO - ✅ 视频管理器初始化完成
2025-07-21 18:10:04 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-0
2025-07-21 18:10:04 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-1
2025-07-21 18:10:04 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-2
2025-07-21 18:10:04 - app.services.video.ai_integration - INFO - 分析工作器已启动: worker-3
2025-07-21 18:10:04 - app.services.video.ai_integration - INFO - 批量处理器已启动
2025-07-21 18:10:04 - app.services.video.ai_integration - INFO - 清理工作器已启动
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 处理工作器 worker-0 已启动
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 处理工作器 worker-1 已启动
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 处理工作器 worker-2 已启动
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 处理工作器 worker-3 已启动
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 统计任务已启动
2025-07-21 18:10:04 - app.services.video.video_pipeline - INFO - 健康检查任务已启动
2025-07-21 18:10:04 - app.main - INFO - ✅ 系统启动完成
2025-07-21 18:10:04 - app.main - INFO - 📊 配置缓存: 暂时禁用（简化模式）
2025-07-21 18:10:04 - app.main - INFO - 📹 摄像头管理: 最大支持 100 路
2025-07-21 18:10:04 - app.main - INFO - 🔄 视频管道: 三层优化架构已启用
2025-07-21 18:10:34 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:10:34 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:10:34 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:10:34 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:11:04 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:11:04 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:11:04 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:11:04 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:11:34 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:11:34 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:11:34 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:11:34 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:12:04 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:12:04 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:12:04 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:12:04 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:12:34 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:12:34 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:12:34 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:12:34 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:13:04 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:13:04 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:13:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:13:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:13:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:13:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:13:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:13:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:14:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:14:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:14:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:14:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:14:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:14:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:14:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:14:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:15:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:15:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:15:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:15:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:15:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:15:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:15:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:15:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:16:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:16:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:16:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:16:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:16:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:16:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:16:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:16:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:17:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:17:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:17:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:17:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:17:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:17:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:17:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:17:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:18:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:18:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:18:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:18:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:18:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:18:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:18:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:18:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:19:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:19:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:19:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:19:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:19:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:19:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:19:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:19:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:20:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:20:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:20:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:20:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:20:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:20:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:20:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:20:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:21:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:21:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:21:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:21:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:21:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:21:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:21:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:21:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:22:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:22:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:22:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:22:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:22:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:22:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:22:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:22:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:23:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:23:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:23:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:23:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:23:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:23:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:23:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:23:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:24:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:24:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:24:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:24:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:24:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:24:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:24:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:24:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:25:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:25:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:25:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:25:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:25:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:25:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:25:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:25:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:26:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:26:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:26:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:26:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:26:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:26:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:26:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:26:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:27:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:27:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:27:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:27:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:27:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:27:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:27:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:27:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:28:05 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:28:05 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:28:05 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:28:05 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
2025-07-21 18:28:35 - app.services.video.video_pipeline - ERROR - 检查组件 motion_detector 健康状态失败: 'MotionDetectionManager' object has no attribute 'health_check'
2025-07-21 18:28:35 - app.services.video.video_pipeline - ERROR - 检查组件 pre_filter 健康状态失败: 'PreFilterProcessor' object has no attribute 'health_check'
2025-07-21 18:28:35 - app.services.video.video_pipeline - ERROR - 检查组件 frame_aggregator 健康状态失败: 'FrameAggregator' object has no attribute 'health_check'
2025-07-21 18:28:35 - app.services.video.video_pipeline - ERROR - 检查组件 ai_integration 健康状态失败: 'AIIntegrationService' object has no attribute 'health_check'
