2025-07-21 13:38:40 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:38:42 - app.services.storage.file_storage_service - ERROR - 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA07B15D6A6, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:38:42 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA07B15D6A6, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:39:10 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:39:10 - app.services.storage.file_storage_service - ERROR - 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA7156CE885, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:39:10 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA7156CE885, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:39:10 - __main__ - ERROR - 启动失败: [6000] 存储服务初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542DA7156CE885, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:45:53 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:46:19 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:46:19 - app.services.storage.file_storage_service - ERROR - 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542E0B00E5DB02, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:46:19 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 18542E0B00E5DB02, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 13:46:19 - app.services.storage.file_storage_service - WARNING - ⚠️ 降级到本地文件存储
2025-07-21 13:46:19 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 13:46:19 - __main__ - ERROR - 启动失败: AlertEventRepository.__init__() missing 1 required positional argument: 'db'
2025-07-21 13:51:47 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 13:51:48 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 13:51:48 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 13:51:48 - __main__ - ERROR - 启动失败: AlertEventRepository.__init__() missing 1 required positional argument: 'db'
2025-07-21 14:01:13 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:01:14 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 14:01:14 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 14:01:14 - __main__ - ERROR - 启动失败: AlertService.__init__() missing 1 required positional argument: 'db_session'
2025-07-21 14:18:14 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:19:24 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:19:24 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 14:19:24 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 14:19:44 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:19:45 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 14:19:45 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 14:19:45 - app.services.storage.file_storage_service - WARNING - ⚠️ MinIO已禁用，使用本地文件存储
2025-07-21 14:19:45 - app.services.storage.file_storage_service - INFO - ✅ 本地存储目录已准备: storage\files
2025-07-21 14:19:45 - __main__ - ERROR - 启动失败: no running event loop
2025-07-21 14:52:09 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:52:10 - app.services.storage.file_storage_service - ERROR - 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 185431A2BEC9E42F, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 14:52:10 - app.services.storage.file_storage_service - ERROR - MinIO客户端初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 185431A2BEC9E42F, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 14:52:10 - __main__ - ERROR - 启动失败: [6000] 存储服务初始化失败: [6000] 存储桶操作失败: S3 operation failed; code: SignatureDoesNotMatch, message: The request signature we calculated does not match the signature you provided. Check your key and signing method., resource: /difybn, request_id: 185431A2BEC9E42F, host_id: dd9025bab4ad464b049177c95eb6ebf374d3b3fd1af9251148b658df7ac2e3e8, bucket_name: difybn
2025-07-21 14:59:22 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 14:59:23 - __main__ - ERROR - 启动失败: AlertEventRepository.__init__() missing 1 required positional argument: 'db'
2025-07-21 15:05:54 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 15:05:55 - __main__ - ERROR - 启动失败: AlertService.__init__() missing 1 required positional argument: 'db_session'
2025-07-21 15:12:54 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 15:12:55 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 15:12:55 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 15:12:55 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 15:12:55 - app.core.cache - ERROR - ❌ Redis缓存连接失败: Error 22 connecting to 0.0.0.0:8000. 22.
2025-07-21 15:12:55 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 15:12:55 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 15:12:55 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 15:12:55 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 15:12:55 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 15:12:55 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 15:12:55 - app.main - ERROR - ❌ 系统启动失败: local variable 'get_session' referenced before assignment
2025-07-21 15:12:55 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 15:12:55 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 15:12:55 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 15:23:57 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 15:23:58 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 15:23:58 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 15:23:58 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 15:24:00 - app.core.cache - INFO - ✅ Redis缓存连接成功
2025-07-21 15:24:00 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 15:24:00 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 15:24:00 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 15:24:00 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 15:24:00 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 15:24:00 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 15:24:00 - app.main - ERROR - ❌ 系统启动失败: object async_generator can't be used in 'await' expression
2025-07-21 15:24:00 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 15:24:00 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 15:24:00 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 15:32:43 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 15:32:44 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 15:32:44 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 15:32:44 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 15:32:46 - app.core.cache - INFO - ✅ Redis缓存连接成功
2025-07-21 15:32:46 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 15:32:46 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 15:32:46 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 15:32:46 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 15:32:46 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 15:32:46 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 15:32:46 - app.main - INFO - 🤖 初始化AI分析器...
2025-07-21 15:32:46 - app.main - INFO - 📹 初始化摄像头管理器...
2025-07-21 15:32:46 - app.main - ERROR - ❌ 系统启动失败: 'NoneType' object has no attribute 'add_config_change_listener'
2025-07-21 15:32:46 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 15:32:46 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 15:32:46 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 15:32:46 - app.services.alert.alert_service - INFO - 预警服务已关闭
2025-07-21 17:17:50 - app.core.logger - INFO - 日志系统初始化完成 - 级别: INFO
2025-07-21 17:17:51 - app.main - INFO - 🚀 智能视频监控预警系统启动中...
2025-07-21 17:17:51 - app.main - INFO - 📊 初始化数据库连接...
2025-07-21 17:17:51 - app.main - INFO - 🔄 初始化Redis缓存...
2025-07-21 17:17:53 - app.core.cache - INFO - ✅ Redis缓存连接成功
2025-07-21 17:17:53 - app.main - INFO - ⚙️ 初始化配置服务...
2025-07-21 17:17:53 - app.main - INFO - 🎬 初始化帧缓冲区管理器...
2025-07-21 17:17:53 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已启动
2025-07-21 17:17:53 - app.main - INFO - 💾 初始化文件存储服务...
2025-07-21 17:17:53 - app.main - INFO - 🔗 初始化WebSocket服务...
2025-07-21 17:17:53 - app.main - INFO - 🚨 初始化预警服务...
2025-07-21 17:17:53 - app.main - INFO - 🤖 初始化AI分析器...
2025-07-21 17:17:53 - app.main - INFO - 📹 初始化摄像头管理器...
2025-07-21 17:17:53 - app.services.camera.camera_manager - WARNING - 配置管理器未提供，将跳过配置变更监听
2025-07-21 17:17:53 - app.services.camera.camera_manager - INFO - 摄像头管理器初始化完成，最大支持 100 路摄像头
2025-07-21 17:17:53 - app.services.camera.camera_manager - ERROR - 加载摄像头配置失败: __aenter__
2025-07-21 17:17:53 - app.services.camera.camera_manager - INFO - 摄像头管理器已启动
2025-07-21 17:17:53 - app.main - INFO - 🔄 初始化视频处理管道...
2025-07-21 17:17:53 - app.services.video.video_pipeline - INFO - 视频分析管道初始化完成
2025-07-21 17:17:53 - app.services.video.video_pipeline - INFO - 管道状态变更: stopped -> starting
2025-07-21 17:17:53 - app.services.video.motion_detector - INFO - 运动检测管理器已初始化
2025-07-21 17:17:53 - app.services.video.video_pipeline - ERROR - 初始化管道组件失败: 'MotionDetectionManager' object has no attribute 'start'
2025-07-21 17:17:53 - app.services.video.video_pipeline - INFO - 管道状态变更: starting -> error
2025-07-21 17:17:53 - app.services.video.video_pipeline - ERROR - 启动视频分析管道失败: 'MotionDetectionManager' object has no attribute 'start'
2025-07-21 17:17:53 - app.main - ERROR - ❌ 系统启动失败: [3000] 启动管道失败: 'MotionDetectionManager' object has no attribute 'start'
2025-07-21 17:17:53 - app.services.camera.camera_manager - INFO - 摄像头重连任务已启动
2025-07-21 17:17:53 - app.services.camera.camera_manager - INFO - 摄像头健康检查任务已启动
2025-07-21 17:17:53 - app.core.cache - INFO - Redis缓存连接已关闭
2025-07-21 17:17:53 - app.services.video.frame_buffer - INFO - 帧缓冲区管理器已停止
2025-07-21 17:17:53 - app.services.notification.websocket_service - INFO - WebSocket服务已关闭
2025-07-21 17:17:53 - app.services.alert.alert_service - INFO - 预警服务已关闭
2025-07-21 17:17:53 - app.services.camera.camera_manager - INFO - 所有摄像头流已停止
2025-07-21 17:17:53 - app.services.camera.camera_manager - INFO - 摄像头管理器已停止
