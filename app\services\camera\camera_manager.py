"""
摄像头管理器 - 统一摄像头管理核心
负责摄像头连接、状态监控、配置管理和生命周期管理
"""

import asyncio
import logging
from typing import Dict, List, Optional, Set, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import cv2
import numpy as np

# 临时类定义 - 替代不存在的导入
class ConnectionStatus(Enum):
    """连接状态"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"

class StatusType(Enum):
    """状态类型"""
    ONLINE = "online"
    OFFLINE = "offline"
    ERROR = "error"

@dataclass
class CameraConnection:
    """摄像头连接"""
    camera_id: int
    status: ConnectionStatus = ConnectionStatus.DISCONNECTED
    stream_url: str = ""
    last_update: datetime = field(default_factory=datetime.utcnow)

@dataclass
class CameraStatus:
    """摄像头状态"""
    camera_id: int
    status_type: StatusType = StatusType.OFFLINE
    last_update: datetime = field(default_factory=datetime.utcnow)

class CameraConnector:
    """摄像头连接器（临时实现）"""
    async def start(self):
        pass
    
    async def stop(self):
        pass

class CameraStatusMonitor:
    """摄像头状态监控器（临时实现）"""
    async def start(self):
        pass
    
    async def stop(self):
        pass

from ..config import ConfigurationManager, ConfigChangeType, ConfigChangeEvent
from ..video import FrameBufferManager, VideoFrame
from ...models import Camera
from ...database import get_database
from ...core.exceptions import CameraError, ConfigurationError

logger = logging.getLogger(__name__)


class CameraState(Enum):
    """摄像头状态"""
    INACTIVE = "inactive"          # 未激活
    CONNECTING = "connecting"      # 连接中
    CONNECTED = "connected"        # 已连接
    STREAMING = "streaming"        # 正在流式传输
    ERROR = "error"               # 错误状态
    DISCONNECTED = "disconnected"  # 已断开


@dataclass
class CameraInfo:
    """摄像头信息"""
    camera_id: int
    name: str
    stream_url: str
    state: CameraState = CameraState.INACTIVE
    last_frame_time: Optional[datetime] = None
    error_count: int = 0
    last_error: Optional[str] = None
    connection_time: Optional[datetime] = None
    frame_count: int = 0
    fps: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CameraManagerStats:
    """摄像头管理器统计信息"""
    total_cameras: int = 0
    active_cameras: int = 0
    connected_cameras: int = 0
    streaming_cameras: int = 0
    error_cameras: int = 0
    total_frames: int = 0
    average_fps: float = 0.0
    uptime: timedelta = field(default_factory=timedelta)
    last_update: datetime = field(default_factory=datetime.utcnow)


class CameraManager:
    """摄像头管理器"""
    
    def __init__(
        self,
        config_manager: ConfigurationManager,
        frame_buffer_manager: FrameBufferManager,
        camera_connector: Optional[Any] = None,  # 暂时使用Any类型
        status_monitor: Optional[Any] = None,    # 暂时使用Any类型
        max_cameras: int = 100,
        reconnect_interval: int = 30,
        health_check_interval: int = 60
    ):
        self.config_manager = config_manager
        self.frame_buffer_manager = frame_buffer_manager
        # 创建默认的连接器和监控器
        self.camera_connector = camera_connector or CameraConnector()
        self.status_monitor = status_monitor or CameraStatusMonitor()
        self.max_cameras = max_cameras
        self.reconnect_interval = reconnect_interval
        self.health_check_interval = health_check_interval
        
        # 摄像头信息
        self._cameras: Dict[int, CameraInfo] = {}
        self._connections: Dict[int, CameraConnection] = {}
        self._stream_tasks: Dict[int, asyncio.Task] = {}
        
        # 管理器状态
        self._is_running = False
        self._start_time: Optional[datetime] = None
        
        # 后台任务
        self._reconnect_task: Optional[asyncio.Task] = None
        self._health_check_task: Optional[asyncio.Task] = None
        
        # 统计信息
        self._stats = CameraManagerStats()
        
        # 注册配置变更监听器（如果配置管理器可用）
        if self.config_manager is not None:
            self.config_manager.add_config_change_listener(
                ConfigChangeType.CAMERA_CONFIG,
                self._on_camera_config_changed
            )
        else:
            logger.warning("配置管理器未提供，将跳过配置变更监听")
        
        logger.info(f"摄像头管理器初始化完成，最大支持 {max_cameras} 路摄像头")
    
    async def start(self):
        """启动摄像头管理器"""
        if self._is_running:
            logger.warning("摄像头管理器已在运行")
            return
        
        try:
            self._is_running = True
            self._start_time = datetime.utcnow()
            
            # 启动组件
            await self.camera_connector.start()
            await self.status_monitor.start()
            
            # 启动后台任务
            self._reconnect_task = asyncio.create_task(self._reconnect_loop())
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            
            # 加载摄像头配置
            await self._load_cameras()
            
            logger.info("摄像头管理器已启动")
            
        except Exception as e:
            logger.error(f"启动摄像头管理器失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止摄像头管理器"""
        if not self._is_running:
            return
        
        try:
            self._is_running = False
            
            # 停止所有摄像头流
            await self._stop_all_streams()
            
            # 停止后台任务
            for task in [self._reconnect_task, self._health_check_task]:
                if task and not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            
            # 停止组件
            await self.camera_connector.stop()
            await self.status_monitor.stop()
            
            # 清理资源
            self._cameras.clear()
            self._connections.clear()
            self._stream_tasks.clear()
            
            logger.info("摄像头管理器已停止")
            
        except Exception as e:
            logger.error(f"停止摄像头管理器失败: {e}")
    
    async def add_camera(self, camera_id: int, force_reload: bool = False) -> bool:
        """添加摄像头"""
        try:
            if camera_id in self._cameras and not force_reload:
                logger.warning(f"摄像头 {camera_id} 已存在")
                return True
            
            # 检查摄像头数量限制
            if len(self._cameras) >= self.max_cameras:
                logger.error(f"摄像头数量已达到最大限制 {self.max_cameras}")
                return False
            
            # 从数据库获取摄像头信息
            async with get_database() as db:
                camera = await db.get(Camera, camera_id)
                if not camera:
                    logger.error(f"摄像头 {camera_id} 不存在")
                    return False
            
            # 创建摄像头信息
            camera_info = CameraInfo(
                camera_id=camera_id,
                name=camera.name,
                stream_url=camera.stream_url,
                state=CameraState.INACTIVE
            )
            
            self._cameras[camera_id] = camera_info
            
            # 尝试连接摄像头
            await self._connect_camera(camera_id)
            
            logger.info(f"摄像头 {camera_id} ({camera.name}) 已添加")
            return True
            
        except Exception as e:
            logger.error(f"添加摄像头 {camera_id} 失败: {e}")
            return False
    
    async def remove_camera(self, camera_id: int) -> bool:
        """移除摄像头"""
        try:
            if camera_id not in self._cameras:
                logger.warning(f"摄像头 {camera_id} 不存在")
                return True
            
            # 停止流式传输
            await self._stop_stream(camera_id)
            
            # 断开连接
            await self._disconnect_camera(camera_id)
            
            # 移除摄像头信息
            del self._cameras[camera_id]
            
            logger.info(f"摄像头 {camera_id} 已移除")
            return True
            
        except Exception as e:
            logger.error(f"移除摄像头 {camera_id} 失败: {e}")
            return False
    
    async def start_stream(self, camera_id: int) -> bool:
        """启动摄像头流"""
        try:
            if camera_id not in self._cameras:
                logger.error(f"摄像头 {camera_id} 不存在")
                return False
            
            camera_info = self._cameras[camera_id]
            
            # 检查摄像头状态
            if camera_info.state == CameraState.STREAMING:
                logger.warning(f"摄像头 {camera_id} 已在流式传输")
                return True
            
            if camera_info.state != CameraState.CONNECTED:
                # 尝试连接
                if not await self._connect_camera(camera_id):
                    return False
            
            # 启动流式传输任务
            if camera_id in self._stream_tasks:
                self._stream_tasks[camera_id].cancel()
            
            self._stream_tasks[camera_id] = asyncio.create_task(
                self._stream_loop(camera_id)
            )
            
            camera_info.state = CameraState.STREAMING
            logger.info(f"摄像头 {camera_id} 流式传输已启动")
            return True
            
        except Exception as e:
            logger.error(f"启动摄像头 {camera_id} 流失败: {e}")
            self._cameras[camera_id].state = CameraState.ERROR
            self._cameras[camera_id].last_error = str(e)
            return False
    
    async def stop_stream(self, camera_id: int) -> bool:
        """停止摄像头流"""
        return await self._stop_stream(camera_id)
    
    async def get_camera_info(self, camera_id: int) -> Optional[CameraInfo]:
        """获取摄像头信息"""
        return self._cameras.get(camera_id)
    
    async def get_all_cameras(self) -> Dict[int, CameraInfo]:
        """获取所有摄像头信息"""
        return self._cameras.copy()
    
    async def get_streaming_cameras(self) -> List[int]:
        """获取正在流式传输的摄像头ID列表"""
        return [
            camera_id for camera_id, info in self._cameras.items()
            if info.state == CameraState.STREAMING
        ]
    
    async def capture_snapshot(self, camera_id: int) -> Optional[np.ndarray]:
        """拍摄快照"""
        try:
            if camera_id not in self._cameras:
                logger.error(f"摄像头 {camera_id} 不存在")
                return None
            
            connection = self._connections.get(camera_id)
            if not connection or connection.status != ConnectionStatus.CONNECTED:
                logger.error(f"摄像头 {camera_id} 未连接")
                return None
            
            # 从连接中读取一帧
            frame = await connection.read_frame()
            if frame is not None:
                logger.debug(f"摄像头 {camera_id} 快照已拍摄")
                return frame
            
            logger.warning(f"摄像头 {camera_id} 读取帧失败")
            return None
            
        except Exception as e:
            logger.error(f"拍摄摄像头 {camera_id} 快照失败: {e}")
            return None
    
    async def test_connection(self, camera_id: int) -> bool:
        """测试摄像头连接"""
        try:
            if camera_id not in self._cameras:
                logger.error(f"摄像头 {camera_id} 不存在")
                return False
            
            camera_info = self._cameras[camera_id]
            
            # 使用连接器测试连接
            test_result = await self.camera_connector.test_connection(camera_info.stream_url)
            
            if test_result:
                logger.info(f"摄像头 {camera_id} 连接测试成功")
            else:
                logger.warning(f"摄像头 {camera_id} 连接测试失败")
                camera_info.error_count += 1
                camera_info.last_error = "连接测试失败"
            
            return test_result
            
        except Exception as e:
            logger.error(f"测试摄像头 {camera_id} 连接失败: {e}")
            return False
    
    async def _load_cameras(self):
        """加载摄像头配置"""
        try:
            from app.database import get_database_manager_instance
            from sqlalchemy import select
            from app.models.camera import Camera

            db_manager = await get_database_manager_instance()
            async with db_manager.get_session() as db:
                # 获取所有激活的摄像头
                query = select(Camera).where(Camera.is_active == True)
                result = await db.execute(query)
                cameras = result.scalars().all()
                
                # 并发添加摄像头
                add_tasks = [
                    self.add_camera(camera.id) 
                    for camera in cameras
                ]
                
                if add_tasks:
                    results = await asyncio.gather(*add_tasks, return_exceptions=True)
                    
                    success_count = sum(1 for result in results if result is True)
                    logger.info(f"已加载 {success_count}/{len(cameras)} 个摄像头")
                else:
                    logger.info("没有找到激活的摄像头")
                    
        except Exception as e:
            logger.warning(f"加载摄像头配置失败: {e}，系统将继续启动")
    
    async def _connect_camera(self, camera_id: int) -> bool:
        """连接摄像头"""
        try:
            camera_info = self._cameras[camera_id]
            camera_info.state = CameraState.CONNECTING
            
            # 使用连接器建立连接
            connection = await self.camera_connector.connect(camera_info.stream_url)
            
            if connection and connection.status == ConnectionStatus.CONNECTED:
                self._connections[camera_id] = connection
                camera_info.state = CameraState.CONNECTED
                camera_info.connection_time = datetime.utcnow()
                camera_info.error_count = 0
                camera_info.last_error = None
                
                logger.info(f"摄像头 {camera_id} 连接成功")
                return True
            else:
                camera_info.state = CameraState.ERROR
                camera_info.error_count += 1
                camera_info.last_error = "连接失败"
                
                logger.error(f"摄像头 {camera_id} 连接失败")
                return False
                
        except Exception as e:
            logger.error(f"连接摄像头 {camera_id} 失败: {e}")
            camera_info.state = CameraState.ERROR
            camera_info.error_count += 1
            camera_info.last_error = str(e)
            return False
    
    async def _disconnect_camera(self, camera_id: int) -> bool:
        """断开摄像头连接"""
        try:
            if camera_id in self._connections:
                connection = self._connections[camera_id]
                await connection.disconnect()
                del self._connections[camera_id]
            
            if camera_id in self._cameras:
                self._cameras[camera_id].state = CameraState.DISCONNECTED
                self._cameras[camera_id].connection_time = None
            
            logger.info(f"摄像头 {camera_id} 已断开连接")
            return True
            
        except Exception as e:
            logger.error(f"断开摄像头 {camera_id} 连接失败: {e}")
            return False
    
    async def _stop_stream(self, camera_id: int) -> bool:
        """停止摄像头流"""
        try:
            if camera_id in self._stream_tasks:
                task = self._stream_tasks[camera_id]
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                del self._stream_tasks[camera_id]
            
            if camera_id in self._cameras:
                camera_info = self._cameras[camera_id]
                if camera_info.state == CameraState.STREAMING:
                    camera_info.state = CameraState.CONNECTED
            
            logger.info(f"摄像头 {camera_id} 流式传输已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止摄像头 {camera_id} 流失败: {e}")
            return False
    
    async def _stop_all_streams(self):
        """停止所有摄像头流"""
        try:
            stop_tasks = [
                self._stop_stream(camera_id) 
                for camera_id in list(self._stream_tasks.keys())
            ]
            
            if stop_tasks:
                await asyncio.gather(*stop_tasks, return_exceptions=True)
            
            logger.info("所有摄像头流已停止")
            
        except Exception as e:
            logger.error(f"停止所有摄像头流失败: {e}")
    
    async def _stream_loop(self, camera_id: int):
        """摄像头流循环"""
        logger.info(f"摄像头 {camera_id} 流循环已启动")
        
        camera_info = self._cameras[camera_id]
        connection = self._connections[camera_id]
        frame_count = 0
        last_fps_time = datetime.utcnow()
        
        try:
            while self._is_running and camera_info.state == CameraState.STREAMING:
                # 读取帧
                frame = await connection.read_frame()
                if frame is None:
                    logger.warning(f"摄像头 {camera_id} 读取帧失败")
                    break
                
                # 创建视频帧对象
                video_frame = VideoFrame(
                    frame_id=f"{camera_id}_{frame_count}",
                    source_id=str(camera_id),
                    timestamp=datetime.utcnow(),
                    frame_data=frame,
                    metadata={
                        "camera_id": camera_id,
                        "frame_count": frame_count,
                        "resolution": f"{frame.shape[1]}x{frame.shape[0]}",
                        "channels": frame.shape[2] if len(frame.shape) > 2 else 1
                    }
                )
                
                # 添加到帧缓冲区
                await self.frame_buffer_manager.add_frame(video_frame)
                
                # 更新统计信息
                frame_count += 1
                camera_info.frame_count += 1
                camera_info.last_frame_time = datetime.utcnow()
                
                # 计算FPS
                current_time = datetime.utcnow()
                if (current_time - last_fps_time).total_seconds() >= 1.0:
                    time_diff = (current_time - last_fps_time).total_seconds()
                    camera_info.fps = frame_count / time_diff
                    frame_count = 0
                    last_fps_time = current_time
                
                # 短暂延迟
                await asyncio.sleep(0.033)  # 约30FPS
                
        except asyncio.CancelledError:
            logger.info(f"摄像头 {camera_id} 流循环被取消")
        except Exception as e:
            logger.error(f"摄像头 {camera_id} 流循环错误: {e}")
            camera_info.state = CameraState.ERROR
            camera_info.error_count += 1
            camera_info.last_error = str(e)
        finally:
            # 清理状态
            if camera_info.state == CameraState.STREAMING:
                camera_info.state = CameraState.CONNECTED
            
            logger.info(f"摄像头 {camera_id} 流循环已结束")
    
    async def _reconnect_loop(self):
        """重连循环"""
        logger.info("摄像头重连任务已启动")
        
        while self._is_running:
            try:
                await asyncio.sleep(self.reconnect_interval)
                
                if not self._is_running:
                    break
                
                # 检查需要重连的摄像头
                reconnect_tasks = []
                for camera_id, camera_info in self._cameras.items():
                    if camera_info.state in [CameraState.ERROR, CameraState.DISCONNECTED]:
                        # 限制重连频率
                        if camera_info.error_count < 5:
                            reconnect_tasks.append(self._connect_camera(camera_id))
                
                if reconnect_tasks:
                    await asyncio.gather(*reconnect_tasks, return_exceptions=True)
                    logger.debug(f"尝试重连 {len(reconnect_tasks)} 个摄像头")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"重连循环错误: {e}")
                await asyncio.sleep(60)  # 错误后延迟1分钟
    
    async def _health_check_loop(self):
        """健康检查循环"""
        logger.info("摄像头健康检查任务已启动")
        
        while self._is_running:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                if not self._is_running:
                    break
                
                # 更新统计信息
                await self._update_statistics()
                
                # 检查摄像头健康状态
                await self._check_camera_health()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查循环错误: {e}")
                await asyncio.sleep(120)  # 错误后延迟2分钟
    
    async def _update_statistics(self):
        """更新统计信息"""
        try:
            stats = CameraManagerStats()
            
            # 基本统计
            stats.total_cameras = len(self._cameras)
            stats.active_cameras = sum(
                1 for info in self._cameras.values() 
                if info.state != CameraState.INACTIVE
            )
            stats.connected_cameras = sum(
                1 for info in self._cameras.values() 
                if info.state == CameraState.CONNECTED
            )
            stats.streaming_cameras = sum(
                1 for info in self._cameras.values() 
                if info.state == CameraState.STREAMING
            )
            stats.error_cameras = sum(
                1 for info in self._cameras.values() 
                if info.state == CameraState.ERROR
            )
            
            # 帧统计
            stats.total_frames = sum(info.frame_count for info in self._cameras.values())
            if stats.streaming_cameras > 0:
                stats.average_fps = sum(
                    info.fps for info in self._cameras.values() 
                    if info.state == CameraState.STREAMING
                ) / stats.streaming_cameras
            
            # 运行时间
            if self._start_time:
                stats.uptime = datetime.utcnow() - self._start_time
            
            stats.last_update = datetime.utcnow()
            
            self._stats = stats
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    async def _check_camera_health(self):
        """检查摄像头健康状态"""
        try:
            current_time = datetime.utcnow()
            
            for camera_id, camera_info in self._cameras.items():
                # 检查流式传输摄像头的帧时间
                if camera_info.state == CameraState.STREAMING:
                    if camera_info.last_frame_time:
                        time_diff = (current_time - camera_info.last_frame_time).total_seconds()
                        if time_diff > 30:  # 30秒内没有帧
                            logger.warning(f"摄像头 {camera_id} 超过30秒未产生帧")
                            camera_info.state = CameraState.ERROR
                            camera_info.error_count += 1
                            camera_info.last_error = "帧超时"
                
                # 检查错误次数过多的摄像头
                if camera_info.error_count >= 5:
                    logger.warning(f"摄像头 {camera_id} 错误次数过多，暂停重连")
                    camera_info.state = CameraState.INACTIVE
                    
        except Exception as e:
            logger.error(f"检查摄像头健康状态失败: {e}")
    
    async def _on_camera_config_changed(self, event: ConfigChangeEvent):
        """处理摄像头配置变更事件"""
        try:
            camera_id = int(event.resource_id)
            
            if camera_id in self._cameras:
                logger.info(f"摄像头 {camera_id} 配置已变更，重新加载")
                
                # 重新加载摄像头
                await self.remove_camera(camera_id)
                await self.add_camera(camera_id, force_reload=True)
                
        except Exception as e:
            logger.error(f"处理摄像头配置变更失败: {e}")
    
    def get_statistics(self) -> CameraManagerStats:
        """获取统计信息"""
        return self._stats
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            health_status = {
                "status": "healthy",
                "is_running": self._is_running,
                "stats": {
                    "total_cameras": self._stats.total_cameras,
                    "streaming_cameras": self._stats.streaming_cameras,
                    "error_cameras": self._stats.error_cameras,
                    "average_fps": self._stats.average_fps
                },
                "issues": []
            }
            
            # 检查错误摄像头比例
            if self._stats.total_cameras > 0:
                error_rate = self._stats.error_cameras / self._stats.total_cameras
                if error_rate > 0.3:  # 超过30%的摄像头有错误
                    health_status["issues"].append("错误摄像头比例过高")
            
            # 检查平均FPS
            if self._stats.streaming_cameras > 0 and self._stats.average_fps < 10:
                health_status["issues"].append("平均FPS过低")
            
            # 检查后台任务
            if self._is_running:
                if not self._reconnect_task or self._reconnect_task.done():
                    health_status["issues"].append("重连任务未运行")
                if not self._health_check_task or self._health_check_task.done():
                    health_status["issues"].append("健康检查任务未运行")
            
            if health_status["issues"]:
                health_status["status"] = "warning" if len(health_status["issues"]) < 3 else "unhealthy"
            
            return health_status
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "is_running": self._is_running
            } 