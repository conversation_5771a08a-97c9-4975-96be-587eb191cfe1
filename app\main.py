"""
智能视频监控预警系统 - FastAPI应用主入口
"""
import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.core.config import settings
from app.core.logger import setup_logging
from app.core.exceptions import VideoAIException


# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 智能视频监控预警系统启动中...")
    
    # 存储服务实例，用于关闭时清理
    services = {}
    
    try:
        # 初始化数据库连接（不创建表）
        logger.info("📊 初始化数据库连接...")
        from app.database import get_database_manager_instance
        db_manager = await get_database_manager_instance()
        services['db_manager'] = db_manager
        
        # 初始化Redis缓存
        logger.info("🔄 初始化Redis缓存...")
        from app.core.cache import RedisCache
        redis_cache = RedisCache()
        await redis_cache.connect()
        services['redis_cache'] = redis_cache
        
        # 初始化配置服务 - 简化版本
        logger.info("⚙️ 初始化配置服务...")
        # 暂时跳过ConfigService，使用简化配置
        config_service = None
        configuration_manager = None
        services['config_service'] = config_service
        services['configuration_manager'] = configuration_manager
        
        # 初始化帧缓冲区管理器
        logger.info("🎬 初始化帧缓冲区管理器...")
        from app.services.video.frame_buffer import FrameBufferManager
        frame_buffer_manager = FrameBufferManager(
            max_buffer_size=settings.video.frame_buffer_size,
            max_frame_age=settings.video.frame_max_age,
            max_total_memory_mb=settings.video.max_memory_mb
        )
        await frame_buffer_manager.start()
        services['frame_buffer_manager'] = frame_buffer_manager
        
        # 初始化文件存储服务
        logger.info("💾 初始化文件存储服务...")
        from app.services.storage.file_storage_service import FileStorageService
        storage_service = FileStorageService()
        # await storage_service.initialize()
        services['storage_service'] = storage_service
        
        # 初始化WebSocket服务
        logger.info("🔗 初始化WebSocket服务...")
        from app.services.notification.websocket_service import WebSocketService
        websocket_service = WebSocketService()
        services['websocket_service'] = websocket_service
        
        # 初始化预警服务
        logger.info("🚨 初始化预警服务...")
        from app.services.alert.alert_service import AlertService

        # 创建AlertService实例（不需要数据库会话，在使用时通过依赖注入获取）
        alert_service = AlertService(
            storage_service=storage_service,
            websocket_service=websocket_service
        )
        services['alert_service'] = alert_service

        # 初始化AI分析器
        logger.info("🤖 初始化AI分析器...")
        from app.services.ai import AIAnalyzer

        # 创建AI分析器实例（在使用时通过依赖注入获取数据库会话）
        ai_analyzer = AIAnalyzer()
        services['ai_analyzer'] = ai_analyzer
        
        # 初始化摄像头管理器
        logger.info("📹 初始化摄像头管理器...")
        from app.services.camera import CameraManager
        camera_manager = CameraManager(
            config_manager=None,  # 暂时不使用配置管理器
            frame_buffer_manager=frame_buffer_manager,
            max_cameras=settings.video.max_concurrent_streams
        )
        await camera_manager.start()
        services['camera_manager'] = camera_manager
        
        # 初始化视频处理管道
        logger.info("🔄 初始化视频处理管道...")
        from app.services.video import VideoPipeline, PipelineConfig
        pipeline_config = PipelineConfig(
            batch_size=10,
            max_queue_size=1000,
            max_workers=4,
            enable_pre_filter=True,
            enable_aggregation=True,
            enable_ai_analysis=True
        )
        video_pipeline = VideoPipeline(
            frame_buffer_manager=frame_buffer_manager,
            config_manager=None,  # 暂时不使用配置管理器
            alert_service=alert_service,
            pipeline_config=pipeline_config
        )
        await video_pipeline.start()
        services['video_pipeline'] = video_pipeline
        
        # 初始化视频管理器（保持兼容性）
        logger.info("📺 初始化视频管理器...")
        from app.services.video import VideoManager
        from app.repositories.camera_repository import CameraRepository
        from app.services.config.config_service import ConfigService
        from app.database import get_session

        # 创建必要的依赖
        async with get_session() as db:
            camera_repository = CameraRepository(db)
            config_service = ConfigService()

            video_manager = VideoManager(
                camera_repository=camera_repository,
                config_service=config_service
            )
            services['video_manager'] = video_manager
        
        # 将服务实例存储到app状态中，供路由使用
        app.state.services = services
        
        logger.info("✅ 系统启动完成")
        logger.info(f"📊 配置缓存: 暂时禁用（简化模式）")
        logger.info(f"📹 摄像头管理: 最大支持 {settings.video.max_concurrent_streams} 路")
        logger.info(f"🔄 视频管道: 三层优化架构已启用")
        
    except Exception as e:
        logger.error(f"❌ 系统启动失败: {e}")
        # 清理已启动的服务
        for service_name, service in services.items():
            try:
                if hasattr(service, 'stop'):
                    await service.stop()
                elif hasattr(service, 'close'):
                    await service.close()
                elif hasattr(service, 'disconnect'):
                    await service.disconnect()
            except Exception as cleanup_error:
                logger.error(f"清理服务 {service_name} 时出错: {cleanup_error}")
        raise
    
    yield
    
    # 关闭时执行
    logger.info("🛑 系统关闭中...")
    
    try:
        # 按依赖关系逆序停止服务
        service_stop_order = [
            'video_pipeline',
            'camera_manager', 
            'video_manager',
            'ai_analyzer',
            'alert_service',
            'websocket_service',
            'storage_service',
            'frame_buffer_manager',
            'configuration_manager',
            'config_service',
            'redis_cache'
        ]
        
        for service_name in service_stop_order:
            if service_name in services:
                try:
                    service = services[service_name]
                    logger.info(f"停止服务: {service_name}")
                    
                    if hasattr(service, 'stop'):
                        await service.stop()
                    elif hasattr(service, 'close'):
                        await service.close()
                    elif hasattr(service, 'disconnect'):
                        await service.disconnect()
                        
                except Exception as e:
                    logger.error(f"停止服务 {service_name} 时出错: {e}")
        
        # 关闭数据库连接
        logger.info("📊 关闭数据库连接...")
        from app.database import get_database_manager_instance
        try:
            db_manager = await get_database_manager_instance()
            await db_manager.close()
        except Exception as e:
            logger.error(f"关闭数据库连接时出错: {e}")
        
        logger.info("✅ 系统关闭完成")
        
    except Exception as e:
        logger.error(f"❌ 系统关闭时出错: {e}")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    description=settings.app_description,
    version=settings.app_version,
    debug=settings.server.debug,
    lifespan=lifespan,
    docs_url="/docs" if settings.is_development else None,
    redoc_url="/redoc" if settings.is_development else None,
    openapi_url="/openapi.json" if settings.is_development else None,
)


# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.server.cors_origins,
    allow_credentials=True,
    allow_methods=settings.server.cors_methods,
    allow_headers=settings.server.cors_headers,
)


# 添加可信主机中间件（生产环境）
if settings.is_production:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*.example.com", "localhost"]  # 根据实际需要配置
    )


# 全局异常处理器
@app.exception_handler(VideoAIException)
async def video_ai_exception_handler(request: Request, exc: VideoAIException):
    """自定义异常处理"""
    logger.error(f"VideoAI异常: {exc.message}", exc_info=exc)
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.message,
            "error_code": exc.error_code,
            "details": exc.details,
            "timestamp": exc.timestamp.isoformat(),
        }
    )


@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """HTTP异常处理"""
    logger.warning(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": str(exc.detail),
            "status_code": exc.status_code,
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理"""
    logger.warning(f"请求验证异常: {exc.errors()}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": True,
            "message": "请求参数验证失败",
            "details": exc.errors(),
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=exc)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": True,
            "message": "服务器内部错误" if settings.is_production else str(exc),
        }
    )


# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """请求日志中间件"""
    import time
    
    start_time = time.time()
    
    # 记录请求信息
    logger.info(f"📥 {request.method} {request.url}")
    
    # 处理请求
    response = await call_next(request)
    
    # 计算处理时间
    process_time = time.time() - start_time
    
    # 记录响应信息
    logger.info(
        f"📤 {request.method} {request.url} - "
        f"状态码: {response.status_code} - "
        f"耗时: {process_time:.3f}s"
    )
    
    # 性能监控
    if settings.enable_performance_logging and process_time * 1000 > settings.performance_threshold_ms:
        logger.warning(
            f"⚠️ 慢请求: {request.method} {request.url} - "
            f"耗时: {process_time:.3f}s"
        )
    
    # 添加处理时间到响应头
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


# 健康检查路由
@app.get("/health", tags=["健康检查"])
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment,
    }


# 根路由
@app.get("/", tags=["基本信息"])
async def root():
    """根路由 - 系统信息"""
    return {
        "message": "智能视频监控预警系统 API",
        "version": settings.app_version,
        "docs_url": "/docs" if settings.is_development else None,
        "environment": settings.environment,
    }


# 系统信息路由
@app.get("/info", tags=["基本信息"])
async def system_info():
    """系统信息接口"""
    import sys
    import platform
    
    return {
        "app_name": settings.app_name,
        "app_version": settings.app_version,
        "python_version": sys.version,
        "platform": platform.platform(),
        "environment": settings.environment,
        "features": {
            "video_streams": f"支持{settings.video.max_concurrent_streams}路并发",
            "ai_analysis": f"基于{settings.ai.qwen_model}",
            "storage": "MySQL + MinIO",
            "real_time": "WebSocket实时推送",
        }
    }


# 注册API路由
from app.api import api_router, websocket_router

app.include_router(api_router)
app.include_router(websocket_router)


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.server.host,
        port=settings.server.port,
        reload=settings.server.reload,
        log_level=settings.logging.level.lower(),
    ) 