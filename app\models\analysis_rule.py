"""
分析规则数据模型
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime,
    ForeignKey, DECIMAL, JSON
)
from sqlalchemy.orm import relationship
from .base import BaseModel


class AnalysisRule(BaseModel):
    """分析规则模型"""

    __tablename__ = "v_analysis_rules"

    # 规则基本信息
    name = Column(String(100), nullable=False, comment="规则名称")

    # 关联信息
    store_id = Column(Integer, ForeignKey("v_stores.id"), nullable=True, comment="适用门店ID（NULL表示全局）")
    camera_id = Column(Integer, ForeignKey("v_cameras.id"), nullable=False, comment="摄像头ID")

    # 规则配置
    rule_type = Column(String(50), nullable=False, default="simple", comment="规则类型")
    trigger_conditions = Column(JSON, nullable=False, comment="触发条件配置")
    confidence_threshold = Column(DECIMAL(3, 2), nullable=False, default=0.70, comment="置信度阈值")
    consecutive_frames = Column(Integer, nullable=False, default=3, comment="连续帧数要求")
    time_window_seconds = Column(Integer, default=30, comment="时间窗口（秒）")
    cooldown_seconds = Column(Integer, default=60, comment="冷却时间（秒）")
    severity_override = Column(String(50), comment="严重等级覆盖")
    apply_scope = Column(String(50), nullable=False, default="all_cameras", comment="应用范围")
    is_enabled = Column(Boolean, default=True, nullable=False, comment="是否启用")
    priority = Column(Integer, nullable=False, default=100, comment="规则优先级")

    # 扩展信息
    metadata = Column(JSON, comment="扩展配置")
    
    # 关联关系
    store = relationship("Store", back_populates="analysis_rules")
    camera = relationship("Camera", back_populates="analysis_rules")
    behavior_mappings = relationship("RuleBehaviorMapping", back_populates="analysis_rule", cascade="all, delete-orphan")
    alert_events = relationship("AlertEvent", back_populates="analysis_rule")
    
    def __repr__(self) -> str:
        return f"<AnalysisRule(id={self.id}, name={self.name})>"

    @property
    def is_global_rule(self) -> bool:
        """是否为全局规则"""
        return self.store_id is None

    @property
    def is_store_rule(self) -> bool:
        """是否为门店规则"""
        return self.store_id is not None and self.camera_id is None

    @property
    def is_camera_rule(self) -> bool:
        """是否为摄像头专属规则"""
        return self.camera_id is not None

    @property
    def scope_description(self) -> str:
        """获取作用域描述"""
        if self.is_camera_rule:
            return f"摄像头专属: {self.camera.name}"
        elif self.is_store_rule:
            return f"门店级别: {self.store.name}"
        else:
            return "全局默认"

    # 兼容性属性
    @property
    def rule_name(self) -> str:
        """兼容性属性：规则名称"""
        return self.name

    @property
    def is_active(self) -> bool:
        """兼容性属性：是否启用"""
        return self.is_enabled

    @property
    def continuous_frames(self) -> int:
        """兼容性属性：连续帧数"""
        return self.consecutive_frames

    @property
    def time_window(self) -> int:
        """兼容性属性：时间窗口"""
        return self.time_window_seconds or 30

    @property
    def cooldown_period(self) -> int:
        """兼容性属性：冷却期"""
        return self.cooldown_seconds or 60

    @property
    def trigger_config(self) -> dict:
        """兼容性属性：触发配置"""
        return self.trigger_conditions or {}

    def get_trigger_config(self, key: str, default=None):
        """获取触发配置"""
        if not self.trigger_conditions:
            return default
        return self.trigger_conditions.get(key, default)

    def get_metadata(self, key: str, default=None):
        """获取元数据配置"""
        if not self.metadata:
            return default
        return self.metadata.get(key, default)