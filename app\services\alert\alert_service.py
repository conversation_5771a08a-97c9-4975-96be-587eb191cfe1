"""
预警服务 - 异常事件处理、存储和推送
"""
import asyncio
import logging
import json
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from app.core.config import settings
from app.core.exceptions import AlertServiceException
from app.services.ai.ai_analyzer import AnalysisResult, AnalysisFrame
from app.repositories.alert_event_repository import AlertEventRepository
from app.repositories.camera_repository import CameraRepository
from app.repositories.store_repository import StoreRepository
from app.repositories.analysis_rule_repository import AnalysisRuleRepository
from app.repositories.anomaly_behavior_repository import AnomalyBehaviorRepository
from app.services.storage.file_storage_service import FileStorageService
from app.services.notification.websocket_service import WebSocketService

logger = logging.getLogger(__name__)


@dataclass
class AlertEvent:
    """预警事件数据结构"""
    id: Optional[int] = None
    uuid: str = ""
    store_id: int = 0
    camera_id: int = 0
    behavior_id: int = 0
    rule_id: int = 0
    title: str = ""
    description: str = ""
    severity: str = "medium"
    confidence_score: float = 0.0
    status: str = "pending"
    trigger_time: datetime = None
    ai_analysis_result: Dict[str, Any] = None
    evidence_files: List[str] = None


class AlertService:
    """预警服务"""

    def __init__(self, storage_service=None, websocket_service=None):
        # 服务依赖
        self.file_storage = storage_service or FileStorageService()
        self.websocket_service = websocket_service or WebSocketService()

        # 预警去重缓存
        self._alert_cache = {}
        self._cache_cleanup_task = None

        # 开始缓存清理任务
        self._start_cache_cleanup()

    async def _get_repositories(self, db_session):
        """获取Repository实例"""
        return {
            'alert_repo': AlertEventRepository(db_session),
            'camera_repo': CameraRepository(db_session),
            'store_repo': StoreRepository(db_session),
            'rule_repo': AnalysisRuleRepository(db_session),
            'behavior_repo': AnomalyBehaviorRepository(db_session)
        }
    
    async def process_analysis_result(self, analysis_result: AnalysisResult):
        """处理AI分析结果，生成预警事件"""
        try:
            if not analysis_result.detected_behaviors:
                logger.debug(f"摄像头 {analysis_result.camera_id} 未检测到异常行为")
                return
            
            logger.info(
                f"处理摄像头 {analysis_result.camera_id} 的 "
                f"{len(analysis_result.detected_behaviors)} 个异常行为"
            )
            
            # 获取摄像头和门店信息
            camera = await self.camera_repo.get_by_id(analysis_result.camera_id)
            if not camera:
                logger.error(f"摄像头 {analysis_result.camera_id} 不存在")
                return
            
            store = await self.store_repo.get_by_id(camera.store_id)
            if not store:
                logger.error(f"门店 {camera.store_id} 不存在")
                return
            
            # 处理每个检测到的异常行为
            for behavior in analysis_result.detected_behaviors:
                await self._process_single_behavior(
                    analysis_result, 
                    behavior, 
                    camera, 
                    store
                )
                
        except Exception as e:
            logger.error(f"处理分析结果失败: {str(e)}")
            raise AlertServiceException(f"处理分析结果失败: {str(e)}")
    
    async def _process_single_behavior(
        self, 
        analysis_result: AnalysisResult,
        behavior: Dict[str, Any],
        camera: Any,
        store: Any
    ):
        """处理单个异常行为"""
        try:
            behavior_code = behavior.get("behavior_code", "unknown")
            confidence = behavior.get("confidence", 0.0)
            
            # 检查冷却时间（避免重复预警）
            if await self._is_in_cooldown(camera.id, behavior_code):
                logger.debug(f"摄像头 {camera.id} 行为 {behavior_code} 在冷却期内，跳过预警")
                return
            
            # 获取分析规则配置
            rules = await self.rule_repo.get_by_camera_id(camera.id)
            active_rule = max(rules, key=lambda r: r.priority) if rules else None
            
            if not active_rule:
                logger.warning(f"摄像头 {camera.id} 没有有效的分析规则")
                return
            
            # 创建预警事件
            alert_uuid = str(uuid.uuid4())
            
            alert_data = {
                "uuid": alert_uuid,
                "store_id": store.id,
                "camera_id": camera.id,
                "behavior_id": await self._get_behavior_id_by_code(behavior_code),
                "rule_id": active_rule.id,
                "title": self._generate_alert_title(behavior, camera, store),
                "description": self._generate_alert_description(behavior, analysis_result),
                "severity": behavior.get("severity", "medium"),
                "confidence_score": confidence,
                "status": "pending",
                "trigger_time": analysis_result.timestamp,
                "first_detected_at": analysis_result.timestamp,
                "last_detected_at": analysis_result.timestamp,
                "detection_count": 1,
                "ai_analysis_result": {
                    "raw_response": analysis_result.raw_response,
                    "detected_behavior": behavior,
                    "processing_time": analysis_result.processing_time,
                    "model_version": settings.ai.qwen_model
                },
                "location_info": {
                    "camera_location": camera.location,
                    "store_name": store.name,
                    "store_address": store.address
                }
            }
            
            # 保存预警事件到数据库
            alert_id = await self.alert_repo.create(alert_data)
            
            # 保存证据文件
            evidence_files = await self._save_evidence_files(analysis_result)
            
            # 更新预警事件的证据文件数量
            await self.alert_repo.update(alert_id, {
                "evidence_count": len(evidence_files["image_urls"]) + len(evidence_files["video_urls"])
            })
            
            # 构建完整的预警消息
            alert_message = {
                "id": alert_id,
                "uuid": alert_uuid,
                "store_id": store.id,
                "store_name": store.name,
                "camera_id": camera.id,
                "camera_name": camera.name,
                "camera_location": camera.location,
                "behavior_name": behavior.get("behavior_name", "未知异常"),
                "behavior_code": behavior_code,
                "severity": behavior.get("severity", "medium"),
                "confidence": confidence,
                "title": alert_data["title"],
                "description": alert_data["description"],
                "trigger_time": analysis_result.timestamp.isoformat(),
                "evidence_files": evidence_files,
                "status": "pending"
            }
            
            # 发送WebSocket推送
            await self.websocket_service.broadcast_alert(alert_message)
            
            # 更新冷却缓存
            await self._update_cooldown_cache(camera.id, behavior_code, active_rule.cooldown_seconds)
            
            logger.info(
                f"✅ 生成预警事件 {alert_id}: {store.name}-{camera.name} "
                f"检测到 {behavior.get('behavior_name')} (置信度: {confidence:.2f})"
            )
            
        except Exception as e:
            logger.error(f"处理单个异常行为失败: {str(e)}")
            raise AlertServiceException(f"处理异常行为失败: {str(e)}")
    
    async def _is_in_cooldown(self, camera_id: int, behavior_code: str) -> bool:
        """检查是否在冷却期内"""
        cache_key = f"{camera_id}_{behavior_code}"
        
        if cache_key in self._alert_cache:
            cooldown_until = self._alert_cache[cache_key]["cooldown_until"]
            return datetime.now() < cooldown_until
        
        return False
    
    async def _update_cooldown_cache(self, camera_id: int, behavior_code: str, cooldown_seconds: int):
        """更新冷却缓存"""
        cache_key = f"{camera_id}_{behavior_code}"
        
        self._alert_cache[cache_key] = {
            "cooldown_until": datetime.now() + timedelta(seconds=cooldown_seconds),
            "last_alert_time": datetime.now()
        }
    
    def _generate_alert_title(self, behavior: Dict[str, Any], camera: Any, store: Any) -> str:
        """生成预警标题"""
        behavior_name = behavior.get("name", behavior.get("behavior_name", "异常行为"))
        confidence = behavior.get("confidence", 0.0)

        return f"【{store.name}】{camera.name} 检测到{behavior_name} (置信度: {confidence:.1%})"
    
    def _generate_alert_description(self, behavior: Dict[str, Any], analysis_result: AnalysisResult) -> str:
        """生成预警描述"""
        parts = []
        
        # 行为描述
        if behavior.get("description"):
            parts.append(f"异常行为: {behavior['description']}")
        
        # 位置信息
        if behavior.get("location"):
            parts.append(f"发生位置: {behavior['location']}")
        
        # 置信度信息
        confidence = behavior.get("confidence", 0.0)
        parts.append(f"检测置信度: {confidence:.1%}")
        
        # 整体场景描述
        if analysis_result.description:
            parts.append(f"场景描述: {analysis_result.description}")
        
        # 检测时间
        parts.append(f"检测时间: {analysis_result.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        
        return "\n".join(parts)
    
    async def _get_behavior_id_by_code(self, behavior_code: str) -> int:
        """根据行为代码获取行为ID"""
        try:
            behavior = await self.behavior_repo.get_behavior_by_code(behavior_code)
            if behavior:
                return behavior.id
            else:
                logger.warning(f"未找到行为代码 {behavior_code} 对应的异常行为，使用默认ID 1")
                return 1
        except Exception as e:
            logger.error(f"查询行为代码 {behavior_code} 失败: {str(e)}，使用默认ID 1")
            return 1
    
    async def _save_evidence_files(self, analysis_result: AnalysisResult) -> Dict[str, Any]:
        """保存证据文件（图片和视频）"""
        evidence_files = {
            "image_urls": [],
            "video_urls": []
        }
        
        try:
            # 检查是否有原始帧数据
            if not analysis_result.source_frames:
                logger.warning(f"摄像头 {analysis_result.camera_id} 的分析结果没有原始帧数据，无法保存证据文件")
                return evidence_files
            
            # 从原始帧中选择关键帧保存为证据图片
            key_frames = self._select_key_frames(analysis_result.source_frames)
            
            # 保存证据图片
            for i, frame in enumerate(key_frames):
                try:
                    # 生成文件名
                    filename = f"alert_{analysis_result.camera_id}_{analysis_result.timestamp.strftime('%Y%m%d_%H%M%S')}_{i}.jpg"
                    
                    # 转换帧数据为字节流
                    import cv2
                    _, buffer = cv2.imencode('.jpg', frame.image, [cv2.IMWRITE_JPEG_QUALITY, 90])
                    image_bytes = buffer.tobytes()
                    
                    # 保存到文件存储
                    file_url = await self.file_storage.save_evidence_image(
                        filename=filename,
                        image_data=image_bytes,
                        metadata={
                            "camera_id": analysis_result.camera_id,
                            "timestamp": analysis_result.timestamp.isoformat(),
                            "frame_index": frame.frame_index,
                            "motion_score": frame.motion_score,
                            "detected_behaviors": [b["behavior_code"] for b in analysis_result.detected_behaviors]
                        }
                    )
                    
                    evidence_files["image_urls"].append(file_url)
                    logger.debug(f"保存证据图片成功: {filename}")
                    
                except Exception as e:
                    logger.error(f"保存证据图片失败 {filename}: {str(e)}")
                    continue
            
            logger.info(f"为摄像头 {analysis_result.camera_id} 保存了 {len(evidence_files['image_urls'])} 个证据文件")
            
        except Exception as e:
            logger.error(f"保存证据文件失败: {str(e)}")
        
        return evidence_files
    
    def _select_key_frames(self, frames: List, max_frames: int = 3) -> List:
        """从帧列表中选择关键帧"""
        if not frames:
            return []
        
        # 如果帧数不多，直接返回所有帧
        if len(frames) <= max_frames:
            return frames
        
        # 按运动分数排序，选择运动分数最高的帧
        sorted_frames = sorted(frames, key=lambda f: f.motion_score, reverse=True)
        
        # 选择前max_frames个关键帧
        key_frames = sorted_frames[:max_frames]
        
        # 按时间戳重新排序
        key_frames.sort(key=lambda f: f.timestamp)
        
        return key_frames
    
    async def get_alert_by_id(self, alert_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取预警事件"""
        try:
            return await self.alert_repo.get_by_id(alert_id)
        except Exception as e:
            logger.error(f"获取预警事件失败: {str(e)}")
            return None
    
    async def get_alerts_by_camera(
        self, 
        camera_id: int, 
        limit: int = 50,
        status: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取摄像头的预警事件列表"""
        try:
            return await self.alert_repo.get_by_camera_id(
                camera_id, 
                limit=limit,
                status=status
            )
        except Exception as e:
            logger.error(f"获取摄像头预警事件失败: {str(e)}")
            return []
    
    async def update_alert_status(self, alert_id: int, status: str, notes: Optional[str] = None):
        """更新预警状态"""
        try:
            update_data = {
                "status": status,
                "processed_at": datetime.now()
            }
            
            if notes:
                update_data["resolution_notes"] = notes
            
            await self.alert_repo.update(alert_id, update_data)
            
            logger.info(f"更新预警 {alert_id} 状态为: {status}")
            
        except Exception as e:
            logger.error(f"更新预警状态失败: {str(e)}")
            raise AlertServiceException(f"更新预警状态失败: {str(e)}")
    
    def _start_cache_cleanup(self):
        """启动缓存清理任务"""
        async def cleanup_task():
            while True:
                try:
                    await asyncio.sleep(300)  # 每5分钟清理一次
                    current_time = datetime.now()
                    
                    # 清理过期的冷却缓存
                    expired_keys = []
                    for key, data in self._alert_cache.items():
                        if current_time > data["cooldown_until"]:
                            expired_keys.append(key)
                    
                    for key in expired_keys:
                        del self._alert_cache[key]
                    
                    if expired_keys:
                        logger.debug(f"清理了 {len(expired_keys)} 个过期的预警缓存")
                        
                except Exception as e:
                    logger.error(f"缓存清理任务异常: {str(e)}")
        
        self._cache_cleanup_task = asyncio.create_task(cleanup_task())
    
    async def close(self):
        """关闭预警服务"""
        if self._cache_cleanup_task:
            self._cache_cleanup_task.cancel()
            try:
                await self._cache_cleanup_task
            except asyncio.CancelledError:
                pass
        
        logger.info("预警服务已关闭") 