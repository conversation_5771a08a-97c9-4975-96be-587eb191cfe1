"""
门店数据模型
"""

from typing import List, Optional
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.dialects.mysql import POINT
from .base import BaseModel


class Store(BaseModel):
    """门店模型"""
    
    __tablename__ = "v_stores"

    # 基础信息
    name: Mapped[str] = mapped_column(String(100), nullable=False, comment="门店名称")
    code: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, comment="门店编码")
    type: Mapped[str] = mapped_column(String(50), nullable=False, default="standard", comment="门店类型")

    # 地址信息
    district: Mapped[str] = mapped_column(String(50), nullable=False, comment="所在区域")
    address: Mapped[str] = mapped_column(String(255), nullable=False, comment="详细地址")
    coordinates: Mapped[Optional[str]] = mapped_column(POINT, comment="地理坐标")

    # 联系信息
    phone: Mapped[Optional[str]] = mapped_column(String(20), comment="联系电话")
    business_hours: Mapped[Optional[str]] = mapped_column(String(50), comment="营业时间")
    manager_name: Mapped[Optional[str]] = mapped_column(String(50), comment="负责人姓名")
    manager_phone: Mapped[Optional[str]] = mapped_column(String(20), comment="负责人电话")

    # 状态信息
    status: Mapped[str] = mapped_column(String(50), nullable=False, default="normal", comment="门店状态")
    camera_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="摄像头数量")
    online_camera_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="在线摄像头数量")

    # 扩展信息
    metadata: Mapped[Optional[dict]] = mapped_column(JSON, comment="扩展元数据")
    
    # 关联关系
    cameras: Mapped[List["Camera"]] = relationship(
        "Camera", 
        back_populates="store",
        cascade="all, delete-orphan"
    )
    analysis_rules = relationship("AnalysisRule", back_populates="store", cascade="all, delete-orphan")
    alert_events: Mapped[List["AlertEvent"]] = relationship(
        "AlertEvent", 
        back_populates="store"
    )
    
    def __repr__(self) -> str:
        return f"<Store(id={self.id}, name='{self.name}', code='{self.code}')>"
    
    @property
    def active_cameras_count(self) -> int:
        """活跃摄像头数量"""
        return len([cam for cam in self.cameras if cam.status == "online"])

    @property
    def total_cameras_count(self) -> int:
        """总摄像头数量"""
        return len(self.cameras)

    def update_camera_counts(self) -> None:
        """更新摄像头统计数量"""
        self.camera_count = len(self.cameras)
        self.online_camera_count = len([cam for cam in self.cameras if cam.status == "online"])