"""
预警事件数据模型
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime,
    ForeignKey, DECIMAL, Enum as SQLEnum
)
from sqlalchemy.orm import relationship
from enum import Enum
from .base import BaseModel


class AlertStatus(Enum):
    """预警状态枚举"""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    IGNORED = "ignored"
    RESOLVED = "resolved"


class AlertEvent(BaseModel):
    """预警事件模型"""

    __tablename__ = "v_alerts"

    # 唯一标识
    uuid = Column(String(36), nullable=False, comment="预警唯一标识")

    # 关联信息
    store_id = Column(Integer, ForeignKey("v_stores.id"), nullable=False, index=True, comment="门店ID")
    camera_id = Column(Integer, ForeignKey("v_cameras.id"), nullable=False, index=True, comment="摄像头ID")
    behavior_id = Column(Integer, ForeignKey("v_anomaly_behaviors.id"), nullable=False, index=True, comment="异常行为ID")
    rule_id = Column(Integer, ForeignKey("v_analysis_rules.id"), nullable=False, index=True, comment="触发规则ID")

    # 预警基本信息
    title = Column(String(200), nullable=False, comment="预警标题")
    description = Column(Text, comment="预警描述")
    severity = Column(String(50), nullable=False, comment="严重等级")
    confidence_score = Column(DECIMAL(5, 4), nullable=False, comment="置信度分数")

    # 状态信息
    status = Column(String(50), nullable=False, default="pending", comment="处理状态")

    # 时间信息
    trigger_time = Column(DateTime, nullable=False, comment="触发时间")
    first_detected_at = Column(DateTime, nullable=False, comment="首次检测时间")
    last_detected_at = Column(DateTime, nullable=False, comment="最后检测时间")
    detection_count = Column(Integer, nullable=False, default=1, comment="检测次数")

    # AI分析结果
    ai_analysis_result = Column(JSON, comment="AI分析原始结果")
    trigger_frames_info = Column(JSON, comment="触发帧信息")
    location_info = Column(JSON, comment="位置信息")

    # 处理信息
    assigned_to = Column(Integer, comment="分配处理人")
    processed_by = Column(Integer, comment="实际处理人")
    processed_at = Column(DateTime, comment="处理时间")
    resolution_notes = Column(Text, comment="处理备注")

    # 证据和通知
    evidence_count = Column(Integer, nullable=False, default=0, comment="证据文件数量")
    notification_sent = Column(Boolean, default=False, comment="是否已发送通知")
    is_important = Column(Boolean, default=False, comment="是否重要事件")

    # 扩展信息
    tags = Column(JSON, comment="事件标签")
    metadata = Column(JSON, comment="扩展元数据")
    
    # 关联关系
    store = relationship("Store", back_populates="alert_events")
    camera = relationship("Camera", back_populates="alert_events")
    anomaly_behavior = relationship("AnomalyBehavior", back_populates="alert_events")
    analysis_rule = relationship("AnalysisRule", back_populates="alert_events")
    evidence_files = relationship("EvidenceFile", back_populates="alert_event", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<AlertEvent(id={self.id}, title={self.title}, severity={self.severity})>"

    @property
    def is_pending(self) -> bool:
        """是否为待处理状态"""
        return self.status == "pending"

    @property
    def is_confirmed(self) -> bool:
        """是否已确认"""
        return self.status == "confirmed"

    @property
    def is_resolved(self) -> bool:
        """是否已解决"""
        return self.status == "resolved"

    @property
    def is_ignored(self) -> bool:
        """是否被忽略"""
        return self.status == "ignored"

    @property
    def severity_level(self) -> str:
        """获取严重程度"""
        return self.severity

    # 兼容性属性
    @property
    def anomaly_behavior_id(self) -> int:
        """兼容性属性：异常行为ID"""
        return self.behavior_id

    @property
    def analysis_rule_id(self) -> int:
        """兼容性属性：分析规则ID"""
        return self.rule_id

    @property
    def alert_title(self) -> str:
        """兼容性属性：预警标题"""
        return self.title

    @property
    def alert_description(self) -> str:
        """兼容性属性：预警描述"""
        return self.description

    @property
    def alert_level(self) -> str:
        """兼容性属性：预警级别"""
        return self.severity

    @property
    def alert_time(self) -> DateTime:
        """兼容性属性：预警时间"""
        return self.trigger_time

    @property
    def process_notes(self) -> str:
        """兼容性属性：处理备注"""
        return self.resolution_notes
    
    @property
    def evidence_count(self) -> int:
        """证据文件数量"""
        return len(self.evidence_files)
    
    @property
    def has_evidence(self) -> bool:
        """是否有证据文件"""
        return self.evidence_count > 0
    
    def get_tags_list(self) -> list:
        """获取标签列表"""
        if not self.tags:
            return []
        if isinstance(self.tags, list):
            return self.tags
        return []

    def set_tags(self, tags: list) -> None:
        """设置标签"""
        self.tags = tags if tags else None

    def get_metadata(self, key: str, default=None):
        """获取元数据"""
        if not self.metadata:
            return default
        return self.metadata.get(key, default)
    
    def mark_as_processed(self, processor_id: int, notes: str = None) -> None:
        """标记为已处理"""
        from datetime import datetime
        self.processed_at = datetime.utcnow()
        self.processed_by = processor_id
        if notes:
            self.resolution_notes = notes

    def mark_notification_sent(self) -> None:
        """标记通知已发送"""
        self.notification_sent = True

    def increment_detection_count(self) -> None:
        """增加检测次数"""
        from datetime import datetime
        self.detection_count += 1
        self.last_detected_at = datetime.utcnow()

    def update_evidence_count(self, count: int) -> None:
        """更新证据文件数量"""
        self.evidence_count = count