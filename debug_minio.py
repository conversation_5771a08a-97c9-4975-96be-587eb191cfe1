#!/usr/bin/env python3
"""
MinIO连接诊断脚本
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_minio_connection():
    """测试MinIO连接"""
    print("=== MinIO连接测试 ===")
    
    try:
        from minio import Minio
        from minio.error import S3Error
        from dotenv import load_dotenv
        
        # 加载环境变量
        load_dotenv('.env', encoding='utf-8')
        
        # 获取配置
        endpoint = os.getenv('MINIO_ENDPOINT', '*************:9000')
        access_key = os.getenv('MINIO_ACCESS_KEY', 'v3Di4s9aszZC6DzeNqHa')
        secret_key = os.getenv('MINIO_SECRET_KEY', 'YAXnJlXazQsjPvsJhZk9ow3qRfvl8Gglu7P8OpHJ')
        secure = os.getenv('MINIO_SECURE', 'false').lower() == 'true'
        bucket_name = os.getenv('MINIO_BUCKET_NAME', 'difybn')

        # 清理endpoint - 移除协议前缀
        if endpoint.startswith('http://'):
            endpoint = endpoint[7:]
        elif endpoint.startswith('https://'):
            endpoint = endpoint[8:]
        
        print(f"📋 配置信息:")
        print(f"   Endpoint: {endpoint}")
        print(f"   Access Key: {access_key[:8]}...")
        print(f"   Secret Key: {secret_key[:8]}...")
        print(f"   Secure: {secure}")
        print(f"   Bucket: {bucket_name}")
        
        # 创建MinIO客户端
        print("\n🔗 创建MinIO客户端...")
        client = Minio(
            endpoint,
            access_key=access_key,
            secret_key=secret_key,
            secure=secure
        )
        print("✅ MinIO客户端创建成功")
        
        # 测试连接 - 列出存储桶
        print("\n📦 测试连接 - 列出存储桶...")
        try:
            buckets = client.list_buckets()
            print("✅ 连接成功，存储桶列表:")
            for bucket in buckets:
                print(f"   - {bucket.name} (创建时间: {bucket.creation_date})")
        except Exception as e:
            print(f"❌ 列出存储桶失败: {e}")
            return False
        
        # 检查目标存储桶是否存在
        print(f"\n🔍 检查存储桶 '{bucket_name}' 是否存在...")
        try:
            exists = client.bucket_exists(bucket_name)
            if exists:
                print(f"✅ 存储桶 '{bucket_name}' 存在")
            else:
                print(f"⚠️ 存储桶 '{bucket_name}' 不存在")
                
                # 尝试创建存储桶
                print(f"🔨 尝试创建存储桶 '{bucket_name}'...")
                try:
                    client.make_bucket(bucket_name)
                    print(f"✅ 存储桶 '{bucket_name}' 创建成功")
                except Exception as create_e:
                    print(f"❌ 创建存储桶失败: {create_e}")
                    return False
                    
        except Exception as e:
            print(f"❌ 检查存储桶失败: {e}")
            return False
        
        # 测试上传一个小文件
        print(f"\n📤 测试文件上传...")
        try:
            from io import BytesIO
            test_data = b"Hello MinIO Test!"
            test_file = BytesIO(test_data)
            
            object_name = "test/connection_test.txt"
            client.put_object(
                bucket_name,
                object_name,
                test_file,
                len(test_data),
                content_type="text/plain"
            )
            print(f"✅ 测试文件上传成功: {object_name}")
            
            # 测试下载
            print(f"📥 测试文件下载...")
            response = client.get_object(bucket_name, object_name)
            downloaded_data = response.read()
            response.close()
            response.release_conn()
            
            if downloaded_data == test_data:
                print("✅ 测试文件下载成功，数据一致")
            else:
                print("❌ 下载的数据与上传的数据不一致")
                return False
            
            # 清理测试文件
            print(f"🗑️ 清理测试文件...")
            client.remove_object(bucket_name, object_name)
            print("✅ 测试文件清理完成")
            
        except Exception as e:
            print(f"❌ 文件操作测试失败: {e}")
            return False
        
        print("\n🎉 MinIO连接测试全部通过!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装minio库: pip install minio")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_alternative_configs():
    """测试替代配置"""
    print("\n=== 替代配置测试 ===")
    
    # 测试不同的endpoint格式
    alternative_configs = [
        {
            "name": "标准endpoint",
            "endpoint": "*************:9000",
            "secure": False
        },
        {
            "name": "HTTPS endpoint",
            "endpoint": "*************:9000",
            "secure": True
        },
        {
            "name": "localhost测试",
            "endpoint": "localhost:9000",
            "secure": False
        }
    ]
    
    for config in alternative_configs:
        print(f"\n🔧 测试配置: {config['name']}")
        try:
            from minio import Minio
            
            access_key = os.getenv('MINIO_ACCESS_KEY', 'v3Di4s9aszZC6DzeNqHa')
            secret_key = os.getenv('MINIO_SECRET_KEY', 'YAXnJlXazQsjPvsJhZk9ow3qRfvl8Gglu7P8OpHJ')

            # 清理endpoint
            endpoint = config["endpoint"]
            if endpoint.startswith('http://'):
                endpoint = endpoint[7:]
            elif endpoint.startswith('https://'):
                endpoint = endpoint[8:]

            client = Minio(
                endpoint,
                access_key=access_key,
                secret_key=secret_key,
                secure=config["secure"]
            )
            
            # 简单连接测试
            buckets = client.list_buckets()
            print(f"✅ {config['name']} 连接成功")
            
        except Exception as e:
            print(f"❌ {config['name']} 连接失败: {e}")

def main():
    """主函数"""
    print("🔍 MinIO连接诊断工具")
    print("=" * 50)
    
    success = test_minio_connection()
    
    if not success:
        test_alternative_configs()
    
    print("\n" + "=" * 50)
    print("🎯 诊断完成")
    
    if success:
        print("✅ MinIO配置正常，可以正常使用")
    else:
        print("❌ MinIO配置有问题，请检查:")
        print("   1. 网络连接是否正常")
        print("   2. MinIO服务是否运行")
        print("   3. 访问密钥是否正确")
        print("   4. 端点地址是否正确")

if __name__ == "__main__":
    main()
