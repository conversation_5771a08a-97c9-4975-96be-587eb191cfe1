"""
预警事件API路由
"""
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from fastapi.responses import J<PERSON><PERSON>esponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db_session
from app.services.alert.alert_service import AlertService
from app.repositories.alert_event_repository import AlertEventRepository
from app.schemas.alert_schemas import (
    AlertResponse,
    AlertListResponse,
    AlertUpdateRequest,
    AlertStatsResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/alerts", tags=["预警事件"])

# 服务依赖将通过依赖注入获取


@router.get("/", response_model=AlertListResponse)
async def get_alerts(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    camera_id: Optional[int] = Query(None, description="摄像头ID"),
    store_id: Optional[int] = Query(None, description="门店ID"),
    status: Optional[str] = Query(None, description="状态筛选"),
    severity: Optional[str] = Query(None, description="严重等级筛选"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    behavior_code: Optional[str] = Query(None, description="行为代码筛选"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取预警事件列表"""
    try:
        alert_repo = AlertEventRepository(db)
        
        # 构建查询条件
        filters = {}
        if camera_id:
            filters["camera_id"] = camera_id
        if store_id:
            filters["store_id"] = store_id
        if status:
            filters["status"] = status
        if severity:
            filters["severity"] = severity
        if start_time:
            filters["start_time"] = start_time
        if end_time:
            filters["end_time"] = end_time
        if behavior_code:
            filters["behavior_code"] = behavior_code
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询预警事件
        alerts, total = await alert_repo.get_alerts_with_pagination(
            filters=filters,
            limit=page_size,
            offset=offset
        )
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        return AlertListResponse(
            alerts=alerts,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except Exception as e:
        logger.error(f"获取预警列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取预警列表失败: {str(e)}"
        )


@router.get("/{alert_id}", response_model=AlertResponse)
async def get_alert_detail(
    alert_id: int = Path(..., description="预警ID"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取预警事件详情"""
    try:
        # 创建AlertService实例
        alert_service = AlertService(db)
        alert = await alert_service.get_alert_by_id(alert_id)

        if not alert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"预警事件 {alert_id} 不存在"
            )

        return AlertResponse(**alert)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取预警详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取预警详情失败: {str(e)}"
        )


@router.put("/{alert_id}/status")
async def update_alert_status(
    alert_id: int = Path(..., description="预警ID"),
    request: AlertUpdateRequest = None,
    db: AsyncSession = Depends(get_db_session)
):
    """更新预警状态"""
    try:
        # 创建AlertService实例
        alert_service = AlertService(db)
        await alert_service.update_alert_status(
            alert_id=alert_id,
            status=request.status,
            notes=request.notes
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": f"预警 {alert_id} 状态已更新为 {request.status}",
                "timestamp": datetime.now().isoformat()
            }
        )

    except Exception as e:
        logger.error(f"更新预警状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新预警状态失败: {str(e)}"
        )


@router.get("/camera/{camera_id}", response_model=AlertListResponse)
async def get_camera_alerts(
    camera_id: int = Path(..., description="摄像头ID"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制"),
    status_filter: Optional[str] = Query(None, description="状态筛选"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取指定摄像头的预警事件"""
    try:
        # 创建AlertService实例
        alert_service = AlertService(db)
        alerts = await alert_service.get_alerts_by_camera(
            camera_id=camera_id,
            limit=limit,
            status=status_filter
        )

        return AlertListResponse(
            alerts=alerts,
            total=len(alerts),
            page=1,
            page_size=limit,
            total_pages=1
        )

    except Exception as e:
        logger.error(f"获取摄像头预警失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取摄像头预警失败: {str(e)}"
        )


@router.get("/stats/summary", response_model=AlertStatsResponse)
async def get_alert_stats(
    start_time: Optional[datetime] = Query(None, description="统计开始时间"),
    end_time: Optional[datetime] = Query(None, description="统计结束时间"),
    store_id: Optional[int] = Query(None, description="门店ID"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取预警统计信息"""
    try:
        alert_repo = AlertEventRepository(db)
        
        # 设置默认时间范围（最近7天）
        if not start_time:
            start_time = datetime.now() - timedelta(days=7)
        if not end_time:
            end_time = datetime.now()
        
        # 构建查询条件
        filters = {
            "start_time": start_time,
            "end_time": end_time
        }
        if store_id:
            filters["store_id"] = store_id
        
        # 获取统计数据
        stats = await alert_repo.get_alert_statistics(filters)
        
        return AlertStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"获取预警统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取预警统计失败: {str(e)}"
        )


@router.get("/recent/urgent")
async def get_recent_urgent_alerts(
    limit: int = Query(10, ge=1, le=50, description="返回数量"),
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取最近的紧急预警"""
    try:
        alert_repo = AlertEventRepository(db)
        
        # 计算时间范围
        start_time = datetime.now() - timedelta(hours=hours)
        
        # 查询紧急预警
        filters = {
            "start_time": start_time,
            "severity": ["critical", "high"],
            "status": ["pending", "processing"]
        }
        
        alerts, _ = await alert_repo.get_alerts_with_pagination(
            filters=filters,
            limit=limit,
            offset=0,
            order_by="trigger_time",
            order_desc=True
        )
        
        return {
            "alerts": alerts,
            "count": len(alerts),
            "time_range_hours": hours,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取紧急预警失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取紧急预警失败: {str(e)}"
        )


@router.post("/{alert_id}/confirm")
async def confirm_alert(
    alert_id: int = Path(..., description="预警ID"),
    notes: Optional[str] = Query(None, description="确认备注"),
    db: AsyncSession = Depends(get_db_session)
):
    """确认预警事件"""
    try:
        # 创建AlertService实例
        alert_service = AlertService(db)
        await alert_service.update_alert_status(
            alert_id=alert_id,
            status="processing",
            notes=notes or "预警已确认"
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": f"预警 {alert_id} 已确认",
                "timestamp": datetime.now().isoformat()
            }
        )

    except Exception as e:
        logger.error(f"确认预警失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"确认预警失败: {str(e)}"
        )


@router.post("/{alert_id}/resolve")
async def resolve_alert(
    alert_id: int = Path(..., description="预警ID"),
    resolution_notes: str = Query(..., description="处理结果说明"),
    db: AsyncSession = Depends(get_db_session)
):
    """处理完成预警事件"""
    try:
        # 创建AlertService实例
        alert_service = AlertService(db)
        await alert_service.update_alert_status(
            alert_id=alert_id,
            status="resolved",
            notes=resolution_notes
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": f"预警 {alert_id} 已处理完成",
                "timestamp": datetime.now().isoformat()
            }
        )

    except Exception as e:
        logger.error(f"处理预警失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理预警失败: {str(e)}"
        )


@router.post("/{alert_id}/false-positive")
async def mark_false_positive(
    alert_id: int = Path(..., description="预警ID"),
    reason: Optional[str] = Query(None, description="误报原因"),
    db: AsyncSession = Depends(get_db_session)
):
    """标记为误报"""
    try:
        # 创建AlertService实例
        alert_service = AlertService(db)
        await alert_service.update_alert_status(
            alert_id=alert_id,
            status="false_positive",
            notes=reason or "标记为误报"
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": f"预警 {alert_id} 已标记为误报",
                "timestamp": datetime.now().isoformat()
            }
        )

    except Exception as e:
        logger.error(f"标记误报失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"标记误报失败: {str(e)}"
        )