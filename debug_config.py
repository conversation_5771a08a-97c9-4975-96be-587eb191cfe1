#!/usr/bin/env python3
"""
配置调试脚本 - 诊断CORS配置问题
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_env_loading():
    """测试环境变量加载"""
    print("=== 环境变量测试 ===")
    
    # 检查.env文件
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env文件存在")
        
        # 读取.env文件内容
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找CORS相关配置
        cors_lines = [line.strip() for line in lines if 'CORS' in line and not line.strip().startswith('#')]
        print(f"📋 CORS相关配置行: {cors_lines}")
        
        # 检查环境变量是否被设置
        cors_origins = os.getenv('CORS_ORIGINS')
        print(f"🔍 CORS_ORIGINS环境变量值: {repr(cors_origins)}")
        print(f"🔍 CORS_ORIGINS类型: {type(cors_origins)}")
        
    else:
        print("❌ .env文件不存在")

def test_pydantic_parsing():
    """测试Pydantic配置解析"""
    print("\n=== Pydantic解析测试 ===")
    
    try:
        from pydantic import Field, field_validator
        from pydantic_settings import BaseSettings
        from typing import List
        
        class TestServerSettings(BaseSettings):
            """测试服务器配置"""
            cors_origins: List[str] = Field(default_factory=lambda: ["*"], alias="cors_origins_list")

            model_config = {
                "env_file": ".env",
                "env_file_encoding": "utf-8",
                "case_sensitive": False,
                "extra": "ignore",
                "env_ignore_empty": True,
                "env_prefix": ""
            }

            def __init__(self, **kwargs):
                # 临时移除CORS_ORIGINS环境变量，避免自动解析
                original_cors_origins = os.environ.pop('CORS_ORIGINS', None)

                try:
                    # 在初始化前手动处理CORS_ORIGINS
                    if 'cors_origins' not in kwargs and original_cors_origins is not None:
                        print(f"🔧 手动处理CORS_ORIGINS: {repr(original_cors_origins)}")
                        if original_cors_origins == "*":
                            kwargs['cors_origins'] = ["*"]
                        else:
                            kwargs['cors_origins'] = [origin.strip() for origin in original_cors_origins.split(",") if origin.strip()]
                        print(f"🔧 设置kwargs['cors_origins']: {kwargs['cors_origins']}")

                    super().__init__(**kwargs)
                finally:
                    # 恢复环境变量
                    if original_cors_origins is not None:
                        os.environ['CORS_ORIGINS'] = original_cors_origins
        
        # 尝试创建配置实例
        print("🚀 尝试创建配置实例...")
        config = TestServerSettings()
        print(f"✅ 配置创建成功!")
        print(f"📋 cors_origins值: {config.cors_origins}")
        
    except Exception as e:
        print(f"❌ Pydantic解析失败: {e}")
        import traceback
        traceback.print_exc()

def test_manual_env_loading():
    """手动测试环境变量加载"""
    print("\n=== 手动环境变量加载测试 ===")
    
    try:
        from dotenv import load_dotenv
        
        # 手动加载.env文件
        load_dotenv('.env', encoding='utf-8')
        
        cors_origins = os.getenv('CORS_ORIGINS')
        print(f"🔍 手动加载后的CORS_ORIGINS: {repr(cors_origins)}")
        
        # 测试解析逻辑
        if cors_origins:
            if cors_origins == "*":
                result = ["*"]
            else:
                result = [origin.strip() for origin in cors_origins.split(",") if origin.strip()]
            print(f"🔧 手动解析结果: {result}")
        
    except ImportError:
        print("⚠️ python-dotenv未安装，跳过手动加载测试")
    except Exception as e:
        print(f"❌ 手动加载失败: {e}")

def main():
    """主函数"""
    print("🔍 CORS配置问题诊断工具")
    print("=" * 50)
    
    test_env_loading()
    test_pydantic_parsing()
    test_manual_env_loading()
    
    print("\n" + "=" * 50)
    print("🎯 诊断完成")

if __name__ == "__main__":
    main()
