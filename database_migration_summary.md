# 数据库模型调整总结

## 📊 已完成的模型调整

### 1. ✅ Store (门店) → v_stores
**表名变更**: `stores` → `v_stores`

**字段调整**:
- `store_name` → `name`
- `store_code` → `code`
- 新增: `type`, `district`, `phone`, `business_hours`, `manager_name`, `manager_phone`, `status`, `camera_count`, `online_camera_count`, `coordinates`, `metadata`
- 移除: `contact_info`, `description`

**兼容性**: 保留了 `store_name`, `store_code` 等属性作为兼容性访问器

### 2. ✅ Camera (摄像头) → v_cameras
**表名变更**: `cameras` → `v_cameras`

**字段调整**:
- `is_active` + `is_online` → `status` (枚举值)
- 新增: `specific_location`, `mac_address`, `rtsp_backup_url`, `brand`, `model`, `analysis_enabled`, `analysis_fps`, `analysis_scenarios`, `last_heartbeat_at`, `connection_info`, `position_x`, `position_y`, `view_angle`, `metadata`
- 移除: `username`, `password`, `port`, `description`

**兼容性**: 保留了 `is_active`, `is_online` 等属性作为兼容性访问器

### 3. ✅ AnomalyBehavior (异常行为) → v_anomaly_behaviors
**表名变更**: `anomaly_behaviors` → `v_anomaly_behaviors`

**字段调整**:
- `behavior_name` → `name`
- `behavior_code` → `code`
- `severity_level` → `default_severity`
- `is_active` → `is_enabled`
- 新增: `category`, `sort_order`, `metadata`
- 移除: `detection_config`

**兼容性**: 保留了 `behavior_name`, `severity_level` 等属性作为兼容性访问器

### 4. ✅ AnalysisRule (分析规则) → v_analysis_rules
**表名变更**: `analysis_rules` → `v_analysis_rules`

**字段调整**:
- `rule_name` → `name`
- `is_active` → `is_enabled`
- `continuous_frames` → `consecutive_frames`
- `time_window` → `time_window_seconds`
- `cooldown_period` → `cooldown_seconds`
- `trigger_config` → `trigger_conditions`
- 新增: `rule_type`, `severity_override`, `apply_scope`, `priority`, `metadata`
- 移除: `rule_code`, `description`, `start_time`, `end_time`, `effective_days`

**兼容性**: 保留了 `rule_name`, `is_active`, `continuous_frames` 等属性作为兼容性访问器

### 5. ✅ RuleBehaviorMapping (规则行为映射) → v_rule_behavior_mappings
**表名变更**: `rule_behavior_mappings` → `v_rule_behavior_mappings`

**字段调整**:
- `analysis_rule_id` → `rule_id`
- `anomaly_behavior_id` → `behavior_id`
- `is_active` → `is_enabled`
- `custom_confidence_threshold` → `confidence_threshold_override`
- `custom_config` → `custom_params`
- 新增: `severity_override`, `weight`
- 移除: `custom_continuous_frames`, `custom_time_window`, `custom_cooldown_period`

**兼容性**: 保留了 `analysis_rule_id`, `anomaly_behavior_id` 等属性作为兼容性访问器

### 6. ✅ PromptTemplate (提示词模板) → v_prompt_templates
**表名变更**: `prompt_templates` → `v_prompt_templates`

**字段调整**:
- `template_name` → `name`
- `template_code` → `code`
- `template_type` → `category`
- `anomaly_behavior_id` → `behavior_id`
- `prompt_content` → `template_content`
- `is_active` → `is_enabled`
- 新增: `scenario`, `model_type`, `model_version`, `temperature`, `max_tokens`, `system_prompt`, `is_default`, `version`, `performance_score`, `success_rate`, `avg_response_time_ms`
- 移除: `description`, `priority`

**兼容性**: 保留了 `template_name`, `template_type` 等属性作为兼容性访问器

### 7. ✅ AlertEvent (预警事件) → v_alerts
**表名变更**: `alert_events` → `v_alerts`

**字段调整**:
- `anomaly_behavior_id` → `behavior_id`
- `analysis_rule_id` → `rule_id`
- `alert_title` → `title`
- `alert_description` → `description`
- `alert_level` → `severity`
- `alert_time` → `trigger_time`
- `process_notes` → `resolution_notes`
- 新增: `uuid`, `first_detected_at`, `last_detected_at`, `detection_count`, `trigger_frames_info`, `location_info`, `assigned_to`, `evidence_count`, `is_important`, `tags`, `metadata`
- 移除: `detected_objects`, `notification_channels`, `notification_time`

**兼容性**: 保留了 `alert_title`, `alert_level` 等属性作为兼容性访问器

### 8. ✅ EvidenceFile (证据文件) → v_evidence_files
**表名变更**: `evidence_files` → `v_evidence_files`

**字段调整**:
- `alert_event_id` → `alert_id`
- `captured_at` → `capture_time`
- 新增: `file_storage_id`, `evidence_type`, `frame_index`, `detection_confidence`, `is_key_evidence`, `evidence_category`, `analysis_result`, `legal_status`, `chain_of_custody`, `retention_reason`
- 移除: `camera_id`, `file_name`, `file_path`, `file_url`, `file_type`, `file_size`, `mime_type`, `duration`, `width`, `height`, `fps`, `storage_type`, `bucket_name`, `object_key`, `is_processed`, `is_deleted`, `delete_reason`, `uploaded_at`

**兼容性**: 保留了 `alert_event_id`, `captured_at` 等属性作为兼容性访问器

### 9. ✅ BaseModel 增强
**新增字段**:
- `creator`: 创建者
- `create_time`: 创建时间
- `updater`: 更新者  
- `update_time`: 更新时间
- `deleted`: 软删除标记
- `tenant_id`: 多租户支持

**兼容性**: 保留了 `created_at`, `updated_at`, `is_deleted` 等属性作为兼容性访问器

## 🔧 依赖关系修复

### 已修复的文件:
1. **app/models/rule_behavior_mapping.py**: 修复了 `behavior_name` 和 `rule_name` 属性访问
2. **app/services/alert/alert_service.py**: 修复了行为名称字段引用
3. **app/schemas/camera_schemas.py**: 确认字段引用正确

### Repository层状态:
- ✅ 所有Repository已正确使用新字段名
- ✅ 查询条件已更新为新字段
- ✅ 关联关系已正确配置

## 📋 下一步操作

1. **数据库迁移**: 需要执行SQL脚本创建新表结构
2. **数据迁移**: 将现有数据从旧表迁移到新表
3. **测试验证**: 验证所有功能正常工作
4. **性能优化**: 根据新表结构优化索引

## ⚠️ 注意事项

1. **向后兼容**: 所有模型都保留了兼容性属性，现有代码无需大量修改
2. **外键关系**: 所有外键关系已更新为新表名
3. **索引优化**: 新表结构可能需要重新优化索引
4. **缓存清理**: 部署后需要清理相关缓存

## 🎯 验证清单

- [x] 模型字段调整完成
- [x] 表名更新完成
- [x] 兼容性属性添加完成
- [x] Repository层适配完成
- [x] 服务层关键引用修复完成
- [ ] 数据库迁移脚本执行
- [ ] 功能测试验证
- [ ] 性能测试验证
