"""
证据文件数据模型
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime,
    ForeignKey, BigInteger, Enum as SQLEnum
)
from sqlalchemy.orm import relationship
from enum import Enum
from .base import BaseModel


class FileType(Enum):
    """文件类型枚举"""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    OTHER = "other"


class EvidenceFile(BaseModel):
    """证据文件模型"""

    __tablename__ = "v_evidence_files"

    # 关联信息
    alert_id = Column(Integer, ForeignKey("v_alerts.id"), nullable=False, index=True, comment="关联预警ID")
    file_storage_id = Column(Integer, ForeignKey("v_file_storage.id"), nullable=False, comment="关联文件存储ID")

    # 证据信息
    evidence_type = Column(String(50), nullable=False, default="auto_capture", comment="证据类型")
    capture_time = Column(DateTime, nullable=False, comment="捕获时间")
    frame_index = Column(Integer, comment="帧索引（图片）")
    detection_confidence = Column(DECIMAL(5, 4), comment="检测置信度")
    is_key_evidence = Column(Boolean, default=False, nullable=False, comment="是否关键证据")
    evidence_category = Column(String(50), nullable=False, default="during_event", comment="证据时间分类")

    # 分析结果
    analysis_result = Column(JSON, comment="分析结果")

    # 法律状态
    legal_status = Column(String(50), nullable=False, default="pending", comment="法律状态")
    chain_of_custody = Column(JSON, comment="监管链记录")
    retention_reason = Column(String(200), comment="保留原因")
    
    # 关联关系
    alert_event = relationship("AlertEvent", back_populates="evidence_files")

    def __repr__(self) -> str:
        return f"<EvidenceFile(id={self.id}, alert_id={self.alert_id}, type={self.evidence_type})>"

    # 兼容性属性
    @property
    def alert_event_id(self) -> int:
        """兼容性属性：预警事件ID"""
        return self.alert_id

    @property
    def captured_at(self) -> DateTime:
        """兼容性属性：拍摄时间"""
        return self.capture_time
    
    def get_analysis_result(self, key: str, default=None):
        """获取分析结果"""
        if not self.analysis_result:
            return default
        return self.analysis_result.get(key, default)

    def get_chain_of_custody(self, key: str, default=None):
        """获取监管链记录"""
        if not self.chain_of_custody:
            return default
        return self.chain_of_custody.get(key, default)

    def add_custody_record(self, action: str, operator: str, timestamp: str = None) -> None:
        """添加监管链记录"""
        if not self.chain_of_custody:
            self.chain_of_custody = []

        if not timestamp:
            from datetime import datetime
            timestamp = datetime.utcnow().isoformat()

        record = {
            "action": action,
            "operator": operator,
            "timestamp": timestamp
        }

        if isinstance(self.chain_of_custody, list):
            self.chain_of_custody.append(record)
        else:
            self.chain_of_custody = [record]

    def mark_as_key_evidence(self, reason: str = None) -> None:
        """标记为关键证据"""
        self.is_key_evidence = True
        if reason:
            self.retention_reason = reason

    def update_legal_status(self, status: str) -> None:
        """更新法律状态"""
        self.legal_status = status