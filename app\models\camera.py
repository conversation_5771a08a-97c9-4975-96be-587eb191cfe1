"""
摄像头数据模型
"""

from typing import List, Optional
from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime,
    ForeignKey, DECIMAL, Enum as SQLEnum, JSON
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from enum import Enum
from .base import BaseModel


class CameraStatus(Enum):
    """摄像头状态枚举"""
    ONLINE = "online"
    OFFLINE = "offline"
    ERROR = "error"
    MAINTENANCE = "maintenance"


class Camera(BaseModel):
    """摄像头模型"""

    __tablename__ = "v_cameras"

    # 门店关联
    store_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("v_stores.id"),
        nullable=False,
        comment="所属门店ID"
    )

    # 基础信息
    name: Mapped[str] = mapped_column(String(100), nullable=False, comment="摄像头名称")
    code: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, comment="设备编号")

    # 位置信息
    location: Mapped[str] = mapped_column(String(100), nullable=False, comment="安装位置描述")
    specific_location: Mapped[Optional[str]] = mapped_column(String(255), comment="具体位置")

    # 网络信息
    ip_address: Mapped[Optional[str]] = mapped_column(String(64), comment="IP地址")
    mac_address: Mapped[Optional[str]] = mapped_column(String(64), comment="MAC地址")

    # 连接信息
    rtsp_url: Mapped[str] = mapped_column(String(500), nullable=False, comment="RTSP流地址")
    rtsp_backup_url: Mapped[Optional[str]] = mapped_column(String(500), comment="备用RTSP流地址")

    # 设备信息
    brand: Mapped[Optional[str]] = mapped_column(String(50), comment="设备品牌")
    model: Mapped[Optional[str]] = mapped_column(String(50), comment="设备型号")

    # 视频参数
    resolution: Mapped[Optional[str]] = mapped_column(String(20), comment="分辨率")
    fps: Mapped[Optional[int]] = mapped_column(Integer, default=25, comment="帧率")

    # AI分析配置
    analysis_enabled: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="是否启用AI分析")
    analysis_fps: Mapped[Optional[float]] = mapped_column(DECIMAL(3, 1), default=1.0, comment="AI分析帧率")
    analysis_scenarios: Mapped[Optional[str]] = mapped_column(Text, comment="适用分析场景")

    # 状态信息
    status: Mapped[str] = mapped_column(String(50), nullable=False, default="offline", comment="设备状态")
    last_heartbeat_at: Mapped[Optional[DateTime]] = mapped_column(DateTime, comment="最后心跳时间")

    # 连接配置
    connection_info: Mapped[Optional[dict]] = mapped_column(JSON, comment="连接信息")

    # 位置坐标
    position_x: Mapped[Optional[float]] = mapped_column(DECIMAL(10, 6), comment="X坐标（相对位置）")
    position_y: Mapped[Optional[float]] = mapped_column(DECIMAL(10, 6), comment="Y坐标（相对位置）")
    view_angle: Mapped[Optional[int]] = mapped_column(Integer, comment="视角角度")

    # 扩展信息
    metadata: Mapped[Optional[dict]] = mapped_column(JSON, comment="扩展元数据")
    
    # 关系映射
    store: Mapped["Store"] = relationship("Store", back_populates="cameras")
    
    analysis_rules: Mapped[List["AnalysisRule"]] = relationship(
        "AnalysisRule", 
        back_populates="camera",
        cascade="all, delete-orphan"
    )
    
    alert_events: Mapped[List["AlertEvent"]] = relationship(
        "AlertEvent", 
        back_populates="camera"
    )
    
    def __repr__(self) -> str:
        return f"<Camera(id={self.id}, name='{self.name}', code='{self.code}')>"
    
    @property
    def is_online(self) -> bool:
        """是否在线"""
        return self.status == "online"

    @property
    def is_active(self) -> bool:
        """是否启用"""
        return self.analysis_enabled

    @property
    def analysis_ready(self) -> bool:
        """分析是否就绪"""
        return self.analysis_enabled and self.is_online