"""
全局配置管理
"""
import os
from typing import Any, Dict, Optional, List
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from pathlib import Path


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    host: str = Field(default="localhost")
    port: int = Field(default=3306)
    username: str = Field(default="root")
    password: str = Field(default="")
    database: str = Field(default="video_ai_analysis")
    charset: str = Field(default="utf8mb4")

    # 连接池配置
    pool_size: int = Field(default=20)
    max_overflow: int = Field(default=30)
    pool_timeout: int = Field(default=30)
    pool_recycle: int = Field(default=3600)

    model_config = {"extra": "ignore"}

    def __init__(self, **kwargs):
        # 手动从环境变量加载数据库配置
        if 'host' not in kwargs:
            kwargs['host'] = os.getenv('DB_HOST', 'localhost')
        if 'port' not in kwargs:
            kwargs['port'] = int(os.getenv('DB_PORT', '3306'))
        if 'username' not in kwargs:
            kwargs['username'] = os.getenv('DB_USER', 'root')
        if 'password' not in kwargs:
            kwargs['password'] = os.getenv('DB_PASSWORD', '')
        if 'database' not in kwargs:
            kwargs['database'] = os.getenv('DB_NAME', 'video_ai_analysis')
        if 'charset' not in kwargs:
            kwargs['charset'] = os.getenv('DB_CHARSET', 'utf8mb4')
        if 'pool_size' not in kwargs:
            kwargs['pool_size'] = int(os.getenv('DB_POOL_SIZE', '20'))
        if 'max_overflow' not in kwargs:
            kwargs['max_overflow'] = int(os.getenv('DB_MAX_OVERFLOW', '30'))
        if 'pool_timeout' not in kwargs:
            kwargs['pool_timeout'] = int(os.getenv('DB_POOL_TIMEOUT', '30'))
        if 'pool_recycle' not in kwargs:
            kwargs['pool_recycle'] = int(os.getenv('DB_POOL_RECYCLE', '3600'))

        super().__init__(**kwargs)
    
    @property
    def url(self) -> str:
        """数据库连接URL"""
        return f"mysql+aiomysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"


class RedisSettings(BaseSettings):
    """Redis配置"""
    host: str = Field(default="localhost")
    port: int = Field(default=6379)
    password: Optional[str] = Field(default=None)
    database: int = Field(default=0)

    # 连接池配置
    max_connections: int = Field(default=100)
    timeout: int = Field(default=5)

    model_config = {
        "extra": "ignore"
    }

    def __init__(self, **kwargs):
        # 手动从环境变量加载Redis配置
        if 'host' not in kwargs:
            kwargs['host'] = os.getenv('REDIS_HOST', 'localhost')
        if 'port' not in kwargs:
            kwargs['port'] = int(os.getenv('REDIS_PORT', '6379'))
        if 'password' not in kwargs:
            redis_password = os.getenv('REDIS_PASSWORD', '')
            kwargs['password'] = redis_password if redis_password else None
        if 'database' not in kwargs:
            kwargs['database'] = int(os.getenv('REDIS_DB', '0'))
        if 'max_connections' not in kwargs:
            kwargs['max_connections'] = int(os.getenv('REDIS_POOL_SIZE', '100'))
        if 'timeout' not in kwargs:
            kwargs['timeout'] = int(os.getenv('REDIS_TIMEOUT', '5'))

        super().__init__(**kwargs)

    @property
    def url(self) -> str:
        """Redis连接URL"""
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.database}"


class MinIOSettings(BaseSettings):
    """MinIO配置"""
    endpoint: str = Field(default="*************:9000", env="MINIO_ENDPOINT")
    access_key: str = Field(default="v3Di4s9aszZC6DzeNqHa", env="MINIO_ACCESS_KEY")
    secret_key: str = Field(default="YAXnJlXazQsjPvsJhZk9ow3qRfvl8Gglu7P8OpHJ", env="MINIO_SECRET_KEY")
    secure: bool = Field(default=False, env="MINIO_SECURE")
    
    # 存储桶配置
    bucket_name: str = Field(default="difybn", env="MINIO_BUCKET_NAME")
    region: str = Field(default="us-east-1", env="MINIO_REGION")
    
    model_config = {"extra": "ignore"}


class AISettings(BaseSettings):
    """AI服务配置"""
    # 通义千问配置
    qwen_api_key: str = Field(default="", env="AI_API_KEY")  # 修正映射
    qwen_api_secret: str = Field(default="", env="QWEN_API_SECRET")
    qwen_model: str = Field(default="qwen-omni-turbo", env="AI_MODEL_NAME")  # 修正映射
    # https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
    qwen_base_url: str = Field(default="https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", env="QWEN_BASE_URL")
    
    # API调用配置
    max_retries: int = Field(default=3, env="AI_MAX_RETRIES")
    timeout: int = Field(default=30, env="AI_TIMEOUT")
    max_concurrent_requests: int = Field(default=10, env="AI_MAX_CONCURRENT")  # 修正映射
    
    # 性能优化配置
    batch_size: int = Field(default=5, env="BATCH_PROCESSING_SIZE")  # 修正映射
    cooldown_seconds: int = Field(default=1, env="AI_COOLDOWN_SECONDS")
    
    model_config = {"extra": "ignore"}


class VideoSettings(BaseSettings):
    """视频处理配置"""
    # 视频流配置
    max_concurrent_streams: int = Field(default=100, env="VIDEO_MAX_STREAMS")  # 修正映射
    rtsp_timeout: int = Field(default=10, env="VIDEO_TIMEOUT")  # 修正映射
    frame_buffer_size: int = Field(default=30, env="VIDEO_BUFFER_SIZE")  # 修正映射
    
    # 帧处理配置
    default_fps: float = Field(default=25.0, env="VIDEO_DEFAULT_FPS")
    analysis_fps: float = Field(default=1.0, env="VIDEO_FRAME_RATE")  # 修正映射
    motion_threshold: float = Field(default=0.3, env="VIDEO_MOTION_THRESHOLD")
    
    # 帧缓冲配置
    frame_max_age: float = Field(default=30.0, env="VIDEO_FRAME_MAX_AGE")  # 帧最大存活时间（秒）
    max_memory_mb: float = Field(default=1024.0, env="VIDEO_MAX_MEMORY_MB")  # 最大内存使用（MB）
    
    # 性能优化配置
    enable_motion_detection: bool = Field(default=True, env="VIDEO_ENABLE_MOTION_DETECTION")
    frame_skip_threshold: int = Field(default=5, env="VIDEO_FRAME_SKIP_THRESHOLD")
    
    # 存储配置
    video_save_path: str = Field(default="./storage/videos", env="VIDEO_SAVE_PATH")
    image_save_path: str = Field(default="./storage/images", env="IMAGE_SAVE_PATH")
    
    model_config = {"extra": "ignore"}


class ServerSettings(BaseSettings):
    """服务器配置"""
    host: str = Field(default="0.0.0.0", env="HOST")  # 修正映射
    port: int = Field(default=8000, env="PORT")  # 修正映射
    debug: bool = Field(default=False, env="DEBUG")  # 修正映射
    reload: bool = Field(default=False, env="RELOAD")  # 修正映射

    # 安全配置
    secret_key: str = Field(default="video-ai-analysis-secret-key", env="SECRET_KEY")  # 修正映射
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")  # 修正映射

    # CORS配置 - 使用不同的字段名避免自动环境变量映射
    cors_origins: List[str] = Field(default_factory=lambda: ["*"], alias="cors_origins_list")
    cors_methods: List[str] = Field(default=["*"], env="SERVER_CORS_METHODS")
    cors_headers: List[str] = Field(default=["*"], env="SERVER_CORS_HEADERS")

    model_config = {
        "extra": "ignore",
        "env_ignore_empty": True,
        "env_prefix": "",
        "case_sensitive": False
    }

    def __init__(self, **kwargs):
        # 临时移除CORS_ORIGINS环境变量，避免自动解析
        original_cors_origins = os.environ.pop('CORS_ORIGINS', None)

        try:
            # 在初始化前手动处理CORS_ORIGINS
            if 'cors_origins' not in kwargs and original_cors_origins is not None:
                if original_cors_origins == "*":
                    kwargs['cors_origins'] = ["*"]
                else:
                    kwargs['cors_origins'] = [origin.strip() for origin in original_cors_origins.split(",") if origin.strip()]

            super().__init__(**kwargs)
        finally:
            # 恢复环境变量
            if original_cors_origins is not None:
                os.environ['CORS_ORIGINS'] = original_cors_origins


class LoggingSettings(BaseSettings):
    """日志配置"""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", env="LOG_FORMAT")
    file_path: str = Field(default="./logs/app.log", env="LOG_FILE_PATH")
    max_bytes: int = Field(default=10485760, env="LOG_MAX_BYTES")  # 10MB
    backup_count: int = Field(default=5, env="LOG_BACKUP_COUNT")
    
    # 结构化日志配置
    enable_json_format: bool = Field(default=False, env="LOG_ENABLE_JSON_FORMAT")
    enable_file_logging: bool = Field(default=True, env="LOG_ENABLE_FILE_LOGGING")
    enable_console_logging: bool = Field(default=True, env="LOG_ENABLE_CONSOLE_LOGGING")
    
    model_config = {"extra": "ignore"}


class Settings(BaseSettings):
    """主配置类"""
    # 环境配置
    environment: str = Field(default="development", env="ENVIRONMENT")
    app_name: str = Field(default="智能视频监控预警系统", env="PROJECT_NAME")
    app_version: str = Field(default="1.0.0", env="VERSION")
    app_description: str = Field(default="智能视频监控预警系统 - 基于AI的实时视频分析服务", env="APP_DESCRIPTION")
    
    # 各模块配置
    database: DatabaseSettings = DatabaseSettings()
    redis: RedisSettings = RedisSettings()
    minio: MinIOSettings = MinIOSettings()
    ai: AISettings = AISettings()
    video: VideoSettings = VideoSettings()
    server: ServerSettings = ServerSettings()
    logging: LoggingSettings = LoggingSettings()
    
    # 监控配置
    enable_monitoring: bool = Field(default=True, env="METRIC_COLLECTION_ENABLED")
    health_check_interval: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")
    
    # 性能配置
    enable_performance_logging: bool = Field(default=True, env="ENABLE_PERFORMANCE_LOGGING")
    performance_threshold_ms: int = Field(default=1000, env="PERFORMANCE_THRESHOLD_MS")
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore"  # 忽略额外字段
    }
        
    @field_validator("environment")
    @classmethod
    def validate_environment(cls, v):
        """验证环境配置"""
        allowed_envs = ["development", "testing", "production"]
        if v not in allowed_envs:
            raise ValueError(f"Environment must be one of {allowed_envs}")
        return v
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment == "development"
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.environment == "testing"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == "production"


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings 