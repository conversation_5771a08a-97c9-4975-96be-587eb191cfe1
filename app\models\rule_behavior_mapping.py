"""
规则行为映射数据模型
"""

from sqlalchemy import (
    Column, Integer, Boolean, ForeignKey, 
    DECIMAL, JSON, UniqueConstraint
)
from sqlalchemy.orm import relationship
from .base import BaseModel


class RuleBehaviorMapping(BaseModel):
    """规则行为映射模型"""

    __tablename__ = "v_rule_behavior_mappings"
    __table_args__ = (
        UniqueConstraint('rule_id', 'behavior_id', name='uq_rule_behavior'),
    )

    # 关联信息
    rule_id = Column(
        Integer,
        ForeignKey("v_analysis_rules.id"),
        nullable=False,
        index=True,
        comment="规则ID"
    )
    behavior_id = Column(
        Integer,
        ForeignKey("v_anomaly_behaviors.id"),
        nullable=False,
        index=True,
        comment="异常行为ID"
    )

    # 映射配置
    is_enabled = Column(Boolean, default=True, nullable=False, comment="是否启用该行为检测")
    confidence_threshold_override = Column(DECIMAL(3, 2), comment="置信度阈值覆盖")
    severity_override = Column(String(50), comment="严重等级覆盖")
    weight = Column(DECIMAL(3, 2), nullable=False, default=1.00, comment="该行为在规则中的权重")

    # 扩展配置
    custom_params = Column(JSON, comment="该行为的自定义参数")
    
    # 关联关系
    analysis_rule = relationship("AnalysisRule", back_populates="behavior_mappings")
    anomaly_behavior = relationship("AnomalyBehavior", back_populates="rule_mappings")
    
    def __repr__(self) -> str:
        return f"<RuleBehaviorMapping(id={self.id}, rule_id={self.rule_id}, behavior_id={self.behavior_id})>"

    def get_effective_confidence_threshold(self) -> float:
        """获取有效的置信度阈值"""
        if self.confidence_threshold_override is not None:
            return float(self.confidence_threshold_override)
        if self.anomaly_behavior and self.anomaly_behavior.default_confidence_threshold:
            return float(self.anomaly_behavior.default_confidence_threshold)
        return 0.7  # 默认值

    def get_effective_severity(self) -> str:
        """获取有效的严重等级"""
        if self.severity_override:
            return self.severity_override
        if self.anomaly_behavior and self.anomaly_behavior.default_severity:
            return self.anomaly_behavior.default_severity
        return "medium"  # 默认值

    def get_custom_params(self, key: str, default=None):
        """获取自定义参数"""
        if not self.custom_params:
            return default
        return self.custom_params.get(key, default)

    # 兼容性属性和方法
    @property
    def analysis_rule_id(self) -> int:
        """兼容性属性：分析规则ID"""
        return self.rule_id

    @property
    def anomaly_behavior_id(self) -> int:
        """兼容性属性：异常行为ID"""
        return self.behavior_id

    @property
    def is_active(self) -> bool:
        """兼容性属性：是否启用"""
        return self.is_enabled

    @property
    def custom_confidence_threshold(self) -> float:
        """兼容性属性：自定义置信度阈值"""
        return self.confidence_threshold_override

    @property
    def custom_config(self) -> dict:
        """兼容性属性：自定义配置"""
        return self.custom_params
    
    @property
    def behavior_name(self) -> str:
        """获取行为名称"""
        return self.anomaly_behavior.name if self.anomaly_behavior else ""

    @property
    def rule_name(self) -> str:
        """获取规则名称"""
        return self.analysis_rule.name if self.analysis_rule else ""