"""
异常行为数据模型
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, 
    DECIMAL, Enum as SQLEnum, JSON
)
from sqlalchemy.orm import relationship
from enum import Enum
from .base import BaseModel


class SeverityLevel(Enum):
    """严重程度枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AnomalyBehavior(BaseModel):
    """异常行为模型"""

    __tablename__ = "v_anomaly_behaviors"

    name = Column(String(100), nullable=False, comment="行为名称")
    code = Column(String(50), nullable=False, comment="行为代码")
    category = Column(String(50), nullable=False, comment="行为分类")
    description = Column(Text, nullable=False, comment="行为描述")
    ai_keywords = Column(JSON, nullable=False, comment="AI识别关键词")
    default_severity = Column(String(50), nullable=False, default="medium", comment="默认严重等级")
    default_confidence_threshold = Column(
        DECIMAL(3, 2),
        nullable=False,
        default=0.70,
        comment="默认置信度阈值"
    )
    is_enabled = Column(Boolean, default=True, nullable=False, comment="是否启用")
    sort_order = Column(Integer, nullable=False, default=0, comment="排序权重")

    # 扩展信息
    metadata = Column(JSON, comment="扩展元数据")
    
    # 关联关系
    rule_mappings = relationship("RuleBehaviorMapping", back_populates="anomaly_behavior")
    prompt_templates = relationship("PromptTemplate", back_populates="anomaly_behavior")
    alert_events = relationship("AlertEvent", back_populates="anomaly_behavior")
    
    def __repr__(self) -> str:
        return f"<AnomalyBehavior(id={self.id}, name={self.name}, code={self.code})>"

    @property
    def is_critical(self) -> bool:
        """是否为关键异常"""
        return self.default_severity == "critical"

    @property
    def keywords_list(self) -> list:
        """获取关键词列表"""
        if not self.ai_keywords:
            return []
        if isinstance(self.ai_keywords, list):
            return self.ai_keywords
        return []

    @property
    def severity_level(self):
        """兼容性属性：获取严重程度枚举"""
        severity_map = {
            "low": SeverityLevel.LOW,
            "medium": SeverityLevel.MEDIUM,
            "high": SeverityLevel.HIGH,
            "critical": SeverityLevel.CRITICAL
        }
        return severity_map.get(self.default_severity, SeverityLevel.MEDIUM)

    def get_metadata(self, key: str, default=None):
        """获取元数据配置"""
        if not self.metadata:
            return default
        return self.metadata.get(key, default)