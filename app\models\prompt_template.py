"""
提示词模板数据模型
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, ForeignKey,
    Enum as SQLEnum, JSON
)
from sqlalchemy.orm import relationship
from enum import Enum
from .base import BaseModel


class TemplateType(Enum):
    """模板类型枚举"""
    GLOBAL = "global"
    BEHAVIOR = "behavior"
    ALGORITHM_BEHAVIOR = "algorithm_behavior"


class PromptTemplate(BaseModel):
    """提示词模板模型"""

    __tablename__ = "v_prompt_templates"

    # 基本信息
    name = Column(String(100), nullable=False, comment="模板名称")
    code = Column(String(50), nullable=False, comment="模板代码")
    category = Column(String(50), nullable=False, comment="模板分类")
    behavior_id = Column(
        Integer,
        ForeignKey("v_anomaly_behaviors.id"),
        nullable=True,
        comment="关联异常行为ID"
    )
    scenario = Column(String(100), comment="适用场景")

    # 模板内容
    template_content = Column(Text, nullable=False, comment="模板内容")
    variables = Column(JSON, comment="模板变量定义")

    # AI模型配置
    model_type = Column(String(50), comment="适用AI模型类型")
    model_version = Column(String(50), comment="适用模型版本")
    temperature = Column(DECIMAL(3, 2), default=0.70, comment="温度参数")
    max_tokens = Column(Integer, default=1000, comment="最大令牌数")
    system_prompt = Column(Text, comment="系统提示词")

    # 状态配置
    is_enabled = Column(Boolean, default=True, nullable=False, comment="是否启用")
    is_default = Column(Boolean, default=False, nullable=False, comment="是否默认模板")
    version = Column(String(20), nullable=False, default="1.0", comment="模板版本")

    # 性能统计
    performance_score = Column(DECIMAL(5, 4), comment="性能评分")
    usage_count = Column(Integer, nullable=False, default=0, comment="使用次数")
    success_rate = Column(DECIMAL(5, 4), comment="成功率")
    avg_response_time_ms = Column(Integer, comment="平均响应时间（毫秒）")
    
    # 关联关系
    anomaly_behavior = relationship("AnomalyBehavior", back_populates="prompt_templates")
    
    def __repr__(self) -> str:
        return f"<PromptTemplate(id={self.id}, name={self.name}, category={self.category})>"

    @property
    def is_global_template(self) -> bool:
        """是否为全局模板"""
        return self.category == "global"

    @property
    def is_behavior_template(self) -> bool:
        """是否为行为默认模板"""
        return self.category == "behavior"

    @property
    def is_algorithm_behavior_template(self) -> bool:
        """是否为算法行为专用模板"""
        return self.category == "algorithm_behavior"

    # 兼容性属性
    @property
    def template_name(self) -> str:
        """兼容性属性：模板名称"""
        return self.name

    @property
    def template_code(self) -> str:
        """兼容性属性：模板编码"""
        return self.code

    @property
    def template_type(self):
        """兼容性属性：模板类型枚举"""
        type_map = {
            "global": TemplateType.GLOBAL,
            "behavior": TemplateType.BEHAVIOR,
            "algorithm_behavior": TemplateType.ALGORITHM_BEHAVIOR
        }
        return type_map.get(self.category, TemplateType.GLOBAL)

    @property
    def anomaly_behavior_id(self) -> int:
        """兼容性属性：异常行为ID"""
        return self.behavior_id

    @property
    def prompt_content(self) -> str:
        """兼容性属性：提示词内容"""
        return self.template_content

    @property
    def is_active(self) -> bool:
        """兼容性属性：是否启用"""
        return self.is_enabled
    
    def get_variable_definition(self, var_name: str) -> dict:
        """获取变量定义"""
        if not self.variables:
            return {}
        return self.variables.get(var_name, {})
    
    def get_all_variables(self) -> list:
        """获取所有变量名"""
        if not self.variables:
            return []
        return list(self.variables.keys())
    
    def render_prompt(self, context: dict = None) -> str:
        """渲染提示词模板"""
        if not context:
            return self.template_content

        content = self.template_content
        for var_name, value in context.items():
            placeholder = f"{{{var_name}}}"
            content = content.replace(placeholder, str(value))

        return content
    
    def validate_variables(self, context: dict) -> list:
        """验证变量是否完整"""
        missing_vars = []
        if not self.variables:
            return missing_vars
        
        for var_name, var_config in self.variables.items():
            required = var_config.get("required", False)
            if required and var_name not in context:
                missing_vars.append(var_name)
        
        return missing_vars
    
    def increment_usage(self) -> None:  
        """增加使用次数"""
        self.usage_count = (self.usage_count or 0) + 1 